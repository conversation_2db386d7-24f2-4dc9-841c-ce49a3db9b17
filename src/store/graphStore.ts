import { create } from 'zustand';
import { Node, Edge, Connection, addEdge, applyNodeChanges, applyEdgeChanges, NodeChange, EdgeChange } from 'reactflow';
import { GraphNodeData, GraphEdgeData, createGraphData, applyForceLayout, applyCircularLayout, applyHierarchicalLayout } from '@/lib/graph-utils';
import { Paper } from '@/store/paperStore';
import { PaperRelationship } from '@/store/relationshipStore';

export type LayoutType = 'force' | 'circular' | 'hierarchical' | 'manual';

interface GraphState {
  // Graph data
  nodes: Node<GraphNodeData>[];
  edges: Edge<GraphEdgeData>[];
  
  // Layout and view state
  currentLayout: LayoutType;
  isLayouting: boolean;
  
  // Selection and interaction state
  selectedNodes: string[];
  selectedEdges: string[];
  hoveredNode: string | null;
  hoveredEdge: string | null;
  
  // View state
  fitViewOnLoad: boolean;
  showMiniMap: boolean;
  showControls: boolean;
  showBackground: boolean;
  
  // Filter state
  filteredRelationshipTypes: string[];
  searchQuery: string;
  yearRange: [number, number] | null;
  
  // Actions
  setNodes: (nodes: Node<GraphNodeData>[]) => void;
  setEdges: (edges: Edge<GraphEdgeData>[]) => void;
  onNodesChange: (changes: NodeChange[]) => void;
  onEdgesChange: (changes: EdgeChange[]) => void;
  onConnect: (connection: Connection) => void;
  
  // Layout actions
  setLayout: (layout: LayoutType) => void;
  applyLayout: (layout: LayoutType) => void;
  
  // Selection actions
  selectNode: (nodeId: string, multi?: boolean) => void;
  selectEdge: (edgeId: string, multi?: boolean) => void;
  clearSelection: () => void;
  setHoveredNode: (nodeId: string | null) => void;
  setHoveredEdge: (edgeId: string | null) => void;
  
  // View actions
  toggleMiniMap: () => void;
  toggleControls: () => void;
  toggleBackground: () => void;
  setFitViewOnLoad: (fit: boolean) => void;
  
  // Filter actions
  setRelationshipTypeFilter: (types: string[]) => void;
  setSearchQuery: (query: string) => void;
  setYearRange: (range: [number, number] | null) => void;
  
  // Data loading
  loadGraphData: (papers: Paper[], relationships: PaperRelationship[]) => void;
  refreshLayout: () => void;
}

export const useGraphStore = create<GraphState>((set, get) => ({
  // Initial state
  nodes: [],
  edges: [],
  currentLayout: 'force',
  isLayouting: false,
  selectedNodes: [],
  selectedEdges: [],
  hoveredNode: null,
  hoveredEdge: null,
  fitViewOnLoad: true,
  showMiniMap: true,
  showControls: true,
  showBackground: true,
  filteredRelationshipTypes: [],
  searchQuery: '',
  yearRange: null,

  // Basic setters
  setNodes: (nodes) => set({ nodes }),
  setEdges: (edges) => set({ edges }),

  // React Flow change handlers
  onNodesChange: (changes) => {
    set({
      nodes: applyNodeChanges(changes, get().nodes),
    });
  },

  onEdgesChange: (changes) => {
    set({
      edges: applyEdgeChanges(changes, get().edges),
    });
  },

  onConnect: (connection) => {
    set({
      edges: addEdge(connection, get().edges),
    });
  },

  // Layout management
  setLayout: (layout) => set({ currentLayout: layout }),

  applyLayout: (layout) => {
    const { nodes, edges } = get();
    set({ isLayouting: true });

    // Apply layout algorithm
    let newNodes: Node<GraphNodeData>[];
    
    switch (layout) {
      case 'force':
        newNodes = applyForceLayout(nodes, edges);
        break;
      case 'circular':
        newNodes = applyCircularLayout(nodes);
        break;
      case 'hierarchical':
        newNodes = applyHierarchicalLayout(nodes, edges);
        break;
      case 'manual':
      default:
        newNodes = nodes; // Keep current positions
        break;
    }

    set({
      nodes: newNodes,
      currentLayout: layout,
      isLayouting: false,
    });
  },

  // Selection management
  selectNode: (nodeId, multi = false) => {
    const { selectedNodes } = get();
    let newSelection: string[];

    if (multi) {
      newSelection = selectedNodes.includes(nodeId)
        ? selectedNodes.filter(id => id !== nodeId)
        : [...selectedNodes, nodeId];
    } else {
      newSelection = selectedNodes.includes(nodeId) && selectedNodes.length === 1
        ? []
        : [nodeId];
    }

    set({ selectedNodes: newSelection });
  },

  selectEdge: (edgeId, multi = false) => {
    const { selectedEdges } = get();
    let newSelection: string[];

    if (multi) {
      newSelection = selectedEdges.includes(edgeId)
        ? selectedEdges.filter(id => id !== edgeId)
        : [...selectedEdges, edgeId];
    } else {
      newSelection = selectedEdges.includes(edgeId) && selectedEdges.length === 1
        ? []
        : [edgeId];
    }

    set({ selectedEdges: newSelection });
  },

  clearSelection: () => set({ selectedNodes: [], selectedEdges: [] }),

  setHoveredNode: (nodeId) => set({ hoveredNode: nodeId }),
  setHoveredEdge: (edgeId) => set({ hoveredEdge: edgeId }),

  // View controls
  toggleMiniMap: () => set((state) => ({ showMiniMap: !state.showMiniMap })),
  toggleControls: () => set((state) => ({ showControls: !state.showControls })),
  toggleBackground: () => set((state) => ({ showBackground: !state.showBackground })),
  setFitViewOnLoad: (fit) => set({ fitViewOnLoad: fit }),

  // Filtering
  setRelationshipTypeFilter: (types) => set({ filteredRelationshipTypes: types }),
  setSearchQuery: (query) => set({ searchQuery: query }),
  setYearRange: (range) => set({ yearRange: range }),

  // Data loading and management
  loadGraphData: (papers, relationships) => {
    const { nodes, edges } = createGraphData(papers, relationships);
    const { currentLayout } = get();
    
    set({ nodes, edges });
    
    // Apply current layout to new data
    setTimeout(() => {
      get().applyLayout(currentLayout);
    }, 100);
  },

  refreshLayout: () => {
    const { currentLayout } = get();
    get().applyLayout(currentLayout);
  },
}));
