import { renderHook, act } from '@testing-library/react'
import { useGraphStore } from '@/store/graphStore'

// Mock the graph utils
jest.mock('@/lib/graph-utils', () => ({
  createGraphData: jest.fn(() => ({
    nodes: [
      {
        id: '1',
        type: 'paperNode',
        position: { x: 100, y: 100 },
        data: { id: '1', type: 'paper', title: 'Test Paper' },
      },
    ],
    edges: [
      {
        id: 'edge-1',
        source: '1',
        target: '2',
        type: 'relationshipEdge',
        data: { type: 'SUPPORTS' },
      },
    ],
  })),
  applyForceLayout: jest.fn((nodes) => nodes),
  applyCircularLayout: jest.fn((nodes) => nodes),
  applyHierarchicalLayout: jest.fn((nodes) => nodes),
}))

describe('graphStore', () => {
  beforeEach(() => {
    // Reset the store before each test
    useGraphStore.setState({
      nodes: [],
      edges: [],
      currentLayout: 'force',
      isLayouting: false,
      selectedNodes: [],
      selectedEdges: [],
      hoveredNode: null,
      hoveredEdge: null,
      fitViewOnLoad: true,
      showMiniMap: true,
      showControls: true,
      showBackground: true,
      filteredRelationshipTypes: [],
      searchQuery: '',
      yearRange: null,
    })
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useGraphStore())
      
      expect(result.current.nodes).toEqual([])
      expect(result.current.edges).toEqual([])
      expect(result.current.currentLayout).toBe('force')
      expect(result.current.isLayouting).toBe(false)
      expect(result.current.selectedNodes).toEqual([])
      expect(result.current.selectedEdges).toEqual([])
      expect(result.current.showMiniMap).toBe(true)
      expect(result.current.showControls).toBe(true)
      expect(result.current.showBackground).toBe(true)
    })
  })

  describe('node selection', () => {
    it('should select a node', () => {
      const { result } = renderHook(() => useGraphStore())
      
      act(() => {
        result.current.selectNode('node-1')
      })
      
      expect(result.current.selectedNodes).toEqual(['node-1'])
    })

    it('should deselect a node when clicked again', () => {
      const { result } = renderHook(() => useGraphStore())
      
      act(() => {
        result.current.selectNode('node-1')
      })
      
      expect(result.current.selectedNodes).toEqual(['node-1'])
      
      act(() => {
        result.current.selectNode('node-1')
      })
      
      expect(result.current.selectedNodes).toEqual([])
    })

    it('should support multi-selection with ctrl/cmd', () => {
      const { result } = renderHook(() => useGraphStore())
      
      act(() => {
        result.current.selectNode('node-1')
      })
      
      act(() => {
        result.current.selectNode('node-2', true) // multi-select
      })
      
      expect(result.current.selectedNodes).toEqual(['node-1', 'node-2'])
    })

    it('should clear all selections', () => {
      const { result } = renderHook(() => useGraphStore())
      
      act(() => {
        result.current.selectNode('node-1')
        result.current.selectEdge('edge-1')
      })
      
      expect(result.current.selectedNodes).toEqual(['node-1'])
      expect(result.current.selectedEdges).toEqual(['edge-1'])
      
      act(() => {
        result.current.clearSelection()
      })
      
      expect(result.current.selectedNodes).toEqual([])
      expect(result.current.selectedEdges).toEqual([])
    })
  })

  describe('layout management', () => {
    it('should change layout', () => {
      const { result } = renderHook(() => useGraphStore())
      
      act(() => {
        result.current.setLayout('circular')
      })
      
      expect(result.current.currentLayout).toBe('circular')
    })

    it('should apply layout and set loading state', () => {
      const { result } = renderHook(() => useGraphStore())
      
      // Set some initial nodes
      act(() => {
        result.current.setNodes([
          {
            id: '1',
            type: 'paperNode',
            position: { x: 0, y: 0 },
            data: { id: '1', type: 'paper', title: 'Test' },
          },
        ])
      })
      
      act(() => {
        result.current.applyLayout('circular')
      })
      
      expect(result.current.currentLayout).toBe('circular')
      expect(result.current.isLayouting).toBe(false) // Should be false after completion
    })
  })

  describe('view controls', () => {
    it('should toggle minimap', () => {
      const { result } = renderHook(() => useGraphStore())
      
      expect(result.current.showMiniMap).toBe(true)
      
      act(() => {
        result.current.toggleMiniMap()
      })
      
      expect(result.current.showMiniMap).toBe(false)
    })

    it('should toggle controls', () => {
      const { result } = renderHook(() => useGraphStore())
      
      expect(result.current.showControls).toBe(true)
      
      act(() => {
        result.current.toggleControls()
      })
      
      expect(result.current.showControls).toBe(false)
    })

    it('should toggle background', () => {
      const { result } = renderHook(() => useGraphStore())
      
      expect(result.current.showBackground).toBe(true)
      
      act(() => {
        result.current.toggleBackground()
      })
      
      expect(result.current.showBackground).toBe(false)
    })
  })

  describe('filtering', () => {
    it('should set search query', () => {
      const { result } = renderHook(() => useGraphStore())
      
      act(() => {
        result.current.setSearchQuery('test query')
      })
      
      expect(result.current.searchQuery).toBe('test query')
    })

    it('should set relationship type filters', () => {
      const { result } = renderHook(() => useGraphStore())
      
      act(() => {
        result.current.setRelationshipTypeFilter(['SUPPORTS', 'CONTRADICTS'])
      })
      
      expect(result.current.filteredRelationshipTypes).toEqual(['SUPPORTS', 'CONTRADICTS'])
    })

    it('should set year range', () => {
      const { result } = renderHook(() => useGraphStore())
      
      act(() => {
        result.current.setYearRange([2020, 2024])
      })
      
      expect(result.current.yearRange).toEqual([2020, 2024])
    })
  })

  describe('data loading', () => {
    it('should load graph data', () => {
      const { result } = renderHook(() => useGraphStore())
      
      const mockPapers = [
        {
          id: '1',
          title: 'Test Paper',
          authors: 'Test Author',
          publicationYear: 2023,
          projectId: 'project-1',
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01',
        },
      ]
      
      const mockRelationships = []
      
      act(() => {
        result.current.loadGraphData(mockPapers, mockRelationships)
      })
      
      expect(result.current.nodes).toHaveLength(1)
      expect(result.current.edges).toHaveLength(1)
    })
  })
})
