import {
  transformPapersToNodes,
  transformRelationshipsToEdges,
  calculateRelationshipCounts,
  createGraphData,
  applyCircularLayout,
  relationshipColors,
  relationshipLabels,
} from '@/lib/graph-utils'

// Mock data
const mockPapers = [
  {
    id: '1',
    title: 'Test Paper 1',
    authors: 'Author 1',
    publicationYear: 2023,
    link: 'https://example.com/1',
    notes: 'Test notes 1',
    projectId: 'project-1',
    createdAt: '2023-01-01',
    updatedAt: '2023-01-01',
  },
  {
    id: '2',
    title: 'Test Paper 2',
    authors: 'Author 2',
    publicationYear: 2024,
    link: 'https://example.com/2',
    notes: 'Test notes 2',
    projectId: 'project-1',
    createdAt: '2023-01-02',
    updatedAt: '2023-01-02',
  },
]

const mockRelationships = [
  {
    id: 'rel-1',
    paperAId: '1',
    paperA: mockPapers[0],
    paperBId: '2',
    paperB: mockPapers[1],
    type: 'SUPPORTS' as const,
    notes: 'Test relationship',
    projectId: 'project-1',
  },
]

describe('graph-utils', () => {
  describe('transformPapersToNodes', () => {
    it('should transform papers to nodes correctly', () => {
      const nodes = transformPapersToNodes(mockPapers)
      
      expect(nodes).toHaveLength(2)
      expect(nodes[0]).toMatchObject({
        id: '1',
        type: 'paperNode',
        data: {
          id: '1',
          type: 'paper',
          title: 'Test Paper 1',
          subtitle: 'Author 1',
          authors: 'Author 1',
          year: 2023,
          notes: 'Test notes 1',
          link: 'https://example.com/1',
          relationshipCount: 0,
        },
        draggable: true,
      })
      
      expect(nodes[0].position).toHaveProperty('x')
      expect(nodes[0].position).toHaveProperty('y')
    })
  })

  describe('transformRelationshipsToEdges', () => {
    it('should transform relationships to edges correctly', () => {
      const edges = transformRelationshipsToEdges(mockRelationships)
      
      expect(edges).toHaveLength(1)
      expect(edges[0]).toMatchObject({
        id: 'rel-1',
        source: '1',
        target: '2',
        type: 'relationshipEdge',
        data: {
          id: 'rel-1',
          source: '1',
          target: '2',
          type: 'SUPPORTS',
          label: 'Supports',
          notes: 'Test relationship',
        },
        label: 'Supports',
        animated: false,
      })
      
      expect(edges[0].style).toHaveProperty('stroke', relationshipColors.SUPPORTS)
      expect(edges[0].markerEnd).toHaveProperty('color', relationshipColors.SUPPORTS)
    })
  })

  describe('calculateRelationshipCounts', () => {
    it('should calculate relationship counts correctly', () => {
      const counts = calculateRelationshipCounts(mockPapers, mockRelationships)
      
      expect(counts).toEqual({
        '1': 1,
        '2': 1,
      })
    })

    it('should handle papers with no relationships', () => {
      const extraPaper = {
        ...mockPapers[0],
        id: '3',
        title: 'Isolated Paper',
      }
      const counts = calculateRelationshipCounts([...mockPapers, extraPaper], mockRelationships)
      
      expect(counts).toEqual({
        '1': 1,
        '2': 1,
        '3': 0,
      })
    })
  })

  describe('createGraphData', () => {
    it('should create complete graph data structure', () => {
      const graphData = createGraphData(mockPapers, mockRelationships)
      
      expect(graphData.nodes).toHaveLength(2)
      expect(graphData.edges).toHaveLength(1)
      
      // Check that relationship counts are applied
      expect(graphData.nodes[0].data.relationshipCount).toBe(1)
      expect(graphData.nodes[1].data.relationshipCount).toBe(1)
    })
  })

  describe('applyCircularLayout', () => {
    it('should apply circular layout to nodes', () => {
      const nodes = transformPapersToNodes(mockPapers)
      const layoutNodes = applyCircularLayout(nodes)
      
      expect(layoutNodes).toHaveLength(2)
      
      // Check that positions are updated
      layoutNodes.forEach(node => {
        expect(node.position.x).toBeGreaterThanOrEqual(0)
        expect(node.position.y).toBeGreaterThanOrEqual(0)
      })
      
      // Check that nodes are positioned in a circle
      const centerX = 400
      const centerY = 300
      const radius = Math.min(centerX, centerY) * 0.8
      
      layoutNodes.forEach((node, index) => {
        const angle = (2 * Math.PI * index) / layoutNodes.length
        const expectedX = centerX + radius * Math.cos(angle)
        const expectedY = centerY + radius * Math.sin(angle)
        
        expect(node.position.x).toBeCloseTo(expectedX, 1)
        expect(node.position.y).toBeCloseTo(expectedY, 1)
      })
    })
  })

  describe('constants', () => {
    it('should have correct relationship colors', () => {
      expect(relationshipColors.SUPPORTS).toBe('#10b981')
      expect(relationshipColors.CONTRADICTS).toBe('#ef4444')
      expect(relationshipColors.COMPLEMENTS).toBe('#3b82f6')
    })

    it('should have correct relationship labels', () => {
      expect(relationshipLabels.SUPPORTS).toBe('Supports')
      expect(relationshipLabels.CONTRADICTS).toBe('Contradicts')
      expect(relationshipLabels.COMPLEMENTS).toBe('Complements')
    })
  })
})
