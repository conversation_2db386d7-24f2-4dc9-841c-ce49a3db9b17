import React from 'react'
import { render, screen } from '@testing-library/react'
import PaperNode from '@/components/graph/PaperNode'
import { GraphNodeData } from '@/lib/graph-utils'

const mockNodeData: GraphNodeData = {
  id: '1',
  type: 'paper',
  title: 'Test Paper Title',
  subtitle: 'Test Authors',
  authors: 'Test Authors',
  year: 2023,
  notes: 'Test notes',
  link: 'https://example.com',
  relationshipCount: 3,
}

const mockNodeProps = {
  id: '1',
  data: mockNodeData,
  selected: false,
  hovered: false,
  type: 'paperNode',
  position: { x: 100, y: 100 },
  dragging: false,
  targetPosition: undefined,
  sourcePosition: undefined,
  dragHandle: undefined,
  isConnectable: true,
  zIndex: 1,
  xPos: 100,
  yPos: 100,
  width: 200,
  height: 100,
}

describe('PaperNode', () => {
  it('should render paper node with basic information', () => {
    render(<PaperNode {...mockNodeProps} />)
    
    expect(screen.getByText('Test Paper Title')).toBeInTheDocument()
    expect(screen.getByText('Test Authors')).toBeInTheDocument()
    expect(screen.getByText('2023')).toBeInTheDocument()
    expect(screen.getByText('3 links')).toBeInTheDocument()
    expect(screen.getByText('Paper')).toBeInTheDocument()
  })

  it('should render selected state correctly', () => {
    render(<PaperNode {...mockNodeProps} selected={true} />)
    
    const card = screen.getByTestId('paper-node-card')
    expect(card).toHaveClass('ring-2', 'ring-blue-500')
  })

  it('should render hovered state correctly', () => {
    render(<PaperNode {...mockNodeProps} hovered={true} />)
    
    const card = screen.getByTestId('paper-node-card')
    expect(card).toHaveClass('shadow-xl')
  })

  it('should handle missing optional data', () => {
    const minimalData: GraphNodeData = {
      id: '1',
      type: 'paper',
      title: 'Minimal Paper',
    }
    
    const minimalProps = {
      ...mockNodeProps,
      data: minimalData,
    }
    
    render(<PaperNode {...minimalProps} />)
    
    expect(screen.getByText('Minimal Paper')).toBeInTheDocument()
    expect(screen.getByText('Paper')).toBeInTheDocument()
    
    // Should not render optional elements
    expect(screen.queryByText('links')).not.toBeInTheDocument()
  })

  it('should render handles for connections', () => {
    render(<PaperNode {...mockNodeProps} />)
    
    const handles = screen.getAllByTestId('react-flow-handle')
    expect(handles).toHaveLength(2) // source and target handles
  })

  it('should show relationship count when available', () => {
    render(<PaperNode {...mockNodeProps} />)
    
    expect(screen.getByText('3 links')).toBeInTheDocument()
  })

  it('should not show relationship count when zero or undefined', () => {
    const dataWithoutLinks = {
      ...mockNodeData,
      relationshipCount: 0,
    }
    
    render(<PaperNode {...mockNodeProps} data={dataWithoutLinks} />)
    
    expect(screen.queryByText('links')).not.toBeInTheDocument()
  })

  it('should display icons for notes and links when available', () => {
    render(<PaperNode {...mockNodeProps} />)
    
    // Check for presence of icons (they should be rendered as SVG elements)
    const icons = screen.getByText('Test Paper Title').closest('div')?.querySelectorAll('svg')
    expect(icons?.length).toBeGreaterThan(0)
  })
})
