"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useGraphStore } from '@/store/graphStore';
import { relationshipLabels, relationshipColors } from '@/lib/graph-utils';
import { BarChart3, Network, Target, Filter } from 'lucide-react';
import { motion } from 'framer-motion';

export function GraphStats() {
  const {
    nodes,
    edges,
    selectedNodes,
    selectedEdges,
    filteredRelationshipTypes,
  } = useGraphStore();

  // Calculate relationship type distribution
  const relationshipStats = edges.reduce((acc, edge) => {
    const type = edge.data?.type;
    if (type) {
      acc[type] = (acc[type] || 0) + 1;
    }
    return acc;
  }, {} as Record<string, number>);

  // Calculate node statistics
  const nodeStats = {
    total: nodes.length,
    selected: selectedNodes.length,
    connected: nodes.filter(node => 
      edges.some(edge => edge.source === node.id || edge.target === node.id)
    ).length,
    isolated: nodes.filter(node => 
      !edges.some(edge => edge.source === node.id || edge.target === node.id)
    ).length,
  };

  const edgeStats = {
    total: edges.length,
    selected: selectedEdges.length,
    filtered: filteredRelationshipTypes.length > 0 
      ? edges.filter(edge => filteredRelationshipTypes.includes(edge.data?.type || '')).length
      : edges.length,
  };

  return (
    <div className="space-y-3">
      {/* Overview Stats */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="w-56 sm:w-48 md:w-56 lg:w-64 shadow-lg border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center space-x-2">
              <BarChart3 className="h-4 w-4 text-green-600" />
              <span>Overview</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <div className="text-center p-2 bg-blue-50 rounded-lg">
                <div className="text-lg font-bold text-blue-700">{nodeStats.total}</div>
                <div className="text-xs text-blue-600">Papers</div>
              </div>
              <div className="text-center p-2 bg-purple-50 rounded-lg">
                <div className="text-lg font-bold text-purple-700">{edgeStats.total}</div>
                <div className="text-xs text-purple-600">Links</div>
              </div>
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Connected:</span>
                <Badge variant="secondary" className="text-xs">
                  {nodeStats.connected}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Isolated:</span>
                <Badge variant="outline" className="text-xs">
                  {nodeStats.isolated}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Selected:</span>
                <Badge variant="default" className="text-xs bg-blue-600">
                  {selectedNodes.length + selectedEdges.length}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Relationship Distribution */}
      {Object.keys(relationshipStats).length > 0 && (
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card className="w-56 sm:w-48 md:w-56 lg:w-64 shadow-lg border-0 bg-white/95 backdrop-blur-sm">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center space-x-2">
                <Network className="h-4 w-4 text-orange-600" />
                <span>Relationships</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {Object.entries(relationshipStats)
                .sort(([,a], [,b]) => b - a)
                .map(([type, count]) => {
                  const label = relationshipLabels[type as keyof typeof relationshipLabels];
                  const color = relationshipColors[type as keyof typeof relationshipColors];
                  const percentage = Math.round((count / edgeStats.total) * 100);
                  
                  return (
                    <div key={type} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: color }}
                          />
                          <span className="text-xs font-medium text-gray-700">
                            {label}
                          </span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {count}
                        </Badge>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div
                          className="h-1.5 rounded-full transition-all duration-300"
                          style={{ 
                            backgroundColor: color, 
                            width: `${percentage}%` 
                          }}
                        />
                      </div>
                    </div>
                  );
                })}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Selection Info */}
      {(selectedNodes.length > 0 || selectedEdges.length > 0) && (
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card className="w-56 sm:w-48 md:w-56 lg:w-64 shadow-lg border-0 bg-blue-50/80 backdrop-blur-sm border-blue-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center space-x-2">
                <Target className="h-4 w-4 text-blue-600" />
                <span>Selection</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {selectedNodes.length > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-xs text-blue-700">Papers:</span>
                  <Badge className="text-xs bg-blue-600">
                    {selectedNodes.length}
                  </Badge>
                </div>
              )}
              {selectedEdges.length > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-xs text-blue-700">Links:</span>
                  <Badge className="text-xs bg-blue-600">
                    {selectedEdges.length}
                  </Badge>
                </div>
              )}
              <div className="text-xs text-blue-600 mt-2 p-2 bg-blue-100 rounded">
                💡 Hold Ctrl/Cmd to select multiple items
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Filter Info */}
      {filteredRelationshipTypes.length > 0 && (
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card className="w-56 sm:w-48 md:w-56 lg:w-64 shadow-lg border-0 bg-amber-50/80 backdrop-blur-sm border-amber-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center space-x-2">
                <Filter className="h-4 w-4 text-amber-600" />
                <span>Active Filters</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs text-amber-700">Showing:</span>
                <Badge className="text-xs bg-amber-600">
                  {edgeStats.filtered} / {edgeStats.total}
                </Badge>
              </div>
              <div className="text-xs text-amber-600">
                {filteredRelationshipTypes.map(type => 
                  relationshipLabels[type as keyof typeof relationshipLabels]
                ).join(', ')}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
