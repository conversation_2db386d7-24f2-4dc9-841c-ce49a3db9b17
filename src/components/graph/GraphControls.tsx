"use client";

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  LayoutGrid, 
  Circle, 
  GitBranch, 
  Hand, 
  ZoomIn, 
  ZoomOut, 
  Maximize, 
  Search,
  Filter,
  Settings,
  Eye,
  EyeOff,
  Map
} from 'lucide-react';
import { useGraphStore, LayoutType } from '@/store/graphStore';
import { relationshipLabels, relationshipColors } from '@/lib/graph-utils';
import { motion } from 'framer-motion';

interface GraphControlsProps {
  onZoomIn: () => void;
  onZoomOut: () => void;
  onFitView: () => void;
}

export function GraphControls({ onZoomIn, onZoomOut, onFitView }: GraphControlsProps) {
  const {
    currentLayout,
    isLayouting,
    searchQuery,
    filteredRelationshipTypes,
    showMiniMap,
    showControls,
    showBackground,
    setLayout,
    applyLayout,
    setSearchQuery,
    setRelationshipTypeFilter,
    toggleMiniMap,
    toggleControls,
    toggleBackground,
  } = useGraphStore();

  const layoutButtons = [
    { key: 'force', label: 'Force', icon: LayoutGrid, description: 'Physics-based layout' },
    { key: 'circular', label: 'Circular', icon: Circle, description: 'Circular arrangement' },
    { key: 'hierarchical', label: 'Hierarchy', icon: GitBranch, description: 'Tree-like structure' },
    { key: 'manual', label: 'Manual', icon: Hand, description: 'Free positioning' },
  ];

  const relationshipTypes = Object.entries(relationshipLabels);

  const handleLayoutChange = (layout: LayoutType) => {
    setLayout(layout);
    applyLayout(layout);
  };

  const handleRelationshipFilter = (type: string) => {
    const newFilters = filteredRelationshipTypes.includes(type)
      ? filteredRelationshipTypes.filter(t => t !== type)
      : [...filteredRelationshipTypes, type];
    setRelationshipTypeFilter(newFilters);
  };

  return (
    <div className="space-y-3">
      {/* Layout Controls */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="w-64 sm:w-56 md:w-64 lg:w-72 shadow-lg border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center space-x-2">
              <LayoutGrid className="h-4 w-4 text-blue-600" />
              <span>Layout Controls</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-2 sm:grid-cols-1 md:grid-cols-2 gap-2">
              {layoutButtons.map(({ key, label, icon: Icon, description }) => (
                <Button
                  key={key}
                  variant={currentLayout === key ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleLayoutChange(key as LayoutType)}
                  disabled={isLayouting}
                  className={`
                    flex items-center space-x-1 transition-all duration-200
                    ${currentLayout === key 
                      ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-md' 
                      : 'hover:bg-blue-50 hover:border-blue-300'
                    }
                  `}
                  title={description}
                >
                  <Icon className="h-3 w-3" />
                  <span>{label}</span>
                </Button>
              ))}
            </div>
            
            <div className="flex space-x-1 pt-2 border-t border-gray-100">
              <Button
                variant="outline"
                size="sm"
                onClick={onZoomIn}
                className="flex-1 hover:bg-green-50 hover:border-green-300"
                title="Zoom In"
              >
                <ZoomIn className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onZoomOut}
                className="flex-1 hover:bg-green-50 hover:border-green-300"
                title="Zoom Out"
              >
                <ZoomOut className="h-3 w-3" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onFitView}
                className="flex-1 hover:bg-green-50 hover:border-green-300"
                title="Fit to View"
              >
                <Maximize className="h-3 w-3" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Search and Filter */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <Card className="w-64 sm:w-56 md:w-64 lg:w-72 shadow-lg border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center space-x-2">
              <Search className="h-4 w-4 text-purple-600" />
              <span>Search & Filter</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
              <Input
                placeholder="Search papers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 text-sm"
              />
            </div>
            
            <div>
              <div className="text-xs font-medium text-gray-600 mb-2 flex items-center space-x-1">
                <Filter className="h-3 w-3" />
                <span>Relationship Types</span>
              </div>
              <div className="space-y-1">
                {relationshipTypes.map(([type, label]) => (
                  <div
                    key={type}
                    className={`
                      flex items-center justify-between p-2 rounded-md cursor-pointer transition-all duration-200
                      ${filteredRelationshipTypes.includes(type)
                        ? 'bg-gray-100 border border-gray-300'
                        : 'hover:bg-gray-50'
                      }
                    `}
                    onClick={() => handleRelationshipFilter(type)}
                  >
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: relationshipColors[type as keyof typeof relationshipColors] }}
                      />
                      <span className="text-xs font-medium">{label}</span>
                    </div>
                    {filteredRelationshipTypes.includes(type) && (
                      <div className="w-2 h-2 bg-blue-600 rounded-full" />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* View Options */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <Card className="w-64 sm:w-56 md:w-64 lg:w-72 shadow-lg border-0 bg-white/95 backdrop-blur-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center space-x-2">
              <Settings className="h-4 w-4 text-gray-600" />
              <span>View Options</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-gray-600">Mini Map</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleMiniMap}
                className={`p-1 h-6 w-6 ${showMiniMap ? 'text-blue-600' : 'text-gray-400'}`}
              >
                {showMiniMap ? <Map className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
              </Button>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-gray-600">Controls</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleControls}
                className={`p-1 h-6 w-6 ${showControls ? 'text-blue-600' : 'text-gray-400'}`}
              >
                {showControls ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
              </Button>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-gray-600">Background</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleBackground}
                className={`p-1 h-6 w-6 ${showBackground ? 'text-blue-600' : 'text-gray-400'}`}
              >
                {showBackground ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
