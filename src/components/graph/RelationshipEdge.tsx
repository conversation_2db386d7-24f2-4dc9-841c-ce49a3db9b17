"use client";

import React, { memo } from 'react';
import { EdgeProps, getBezierPath, EdgeLabelRenderer } from 'reactflow';
import { GraphEdgeData, relationshipColors } from '@/lib/graph-utils';
import { motion } from 'framer-motion';

interface RelationshipEdgeProps extends EdgeProps<GraphEdgeData> {
  selected?: boolean;
  hovered?: boolean;
}

const RelationshipEdge = memo(({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  data,
  selected,
  hovered,
}: RelationshipEdgeProps) => {
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  const edgeColor = data ? relationshipColors[data.type] : '#6b7280';
  const strokeWidth = selected ? 3 : hovered ? 2.5 : 2;
  const opacity = selected ? 1 : hovered ? 0.9 : 0.7;

  return (
    <>
      <motion.path
        id={id}
        style={{
          stroke: edgeColor,
          strokeWidth,
          opacity,
        }}
        className="react-flow__edge-path"
        d={edgePath}
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 0.5, ease: "easeInOut" }}
        markerEnd={`url(#${id}-marker)`}
      />
      
      {/* Custom arrow marker */}
      <defs>
        <marker
          id={`${id}-marker`}
          markerWidth="12"
          markerHeight="12"
          refX="6"
          refY="3"
          orient="auto"
          markerUnits="strokeWidth"
        >
          <polygon
            points="0,0 0,6 9,3"
            fill={edgeColor}
            opacity={opacity}
          />
        </marker>
      </defs>
      
      {data && (
        <EdgeLabelRenderer>
          <motion.div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              pointerEvents: 'all',
            }}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ 
              scale: selected ? 1.1 : hovered ? 1.05 : 1,
              opacity: selected ? 1 : hovered ? 0.9 : 0.8,
            }}
            transition={{ duration: 0.2 }}
            className={`
              px-2 py-1 bg-white border rounded-md shadow-sm text-xs font-medium
              ${selected ? 'border-2 shadow-md' : 'border'}
            `}
            style={{
              borderColor: edgeColor,
              color: edgeColor,
            }}
          >
            {data.label}
          </motion.div>
        </EdgeLabelRenderer>
      )}
    </>
  );
});

RelationshipEdge.displayName = 'RelationshipEdge';

export default RelationshipEdge;
