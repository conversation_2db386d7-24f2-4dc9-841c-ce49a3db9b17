"use client";

import React, { memo } from 'react';
import { Hand<PERSON>, Position, NodeProps } from 'reactflow';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { GraphNodeData } from '@/lib/graph-utils';
import { BookOpen, Users, Calendar, ExternalLink, MessageSquare } from 'lucide-react';
import { motion } from 'framer-motion';

interface PaperNodeProps extends NodeProps<GraphNodeData> {
  selected?: boolean;
  hovered?: boolean;
}

const PaperNode = memo(({ data, selected, hovered }: PaperNodeProps) => {
  const {
    title,
    authors,
    year,
    relationshipCount,
    notes,
    link,
  } = data;

  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{
        scale: selected ? 1.08 : hovered ? 1.04 : 1,
        opacity: 1,
      }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30,
        duration: 0.3
      }}
      className="relative"
      whileHover={{ y: -2 }}
    >
      <Handle
        type="target"
        position={Position.Top}
        className={`
          w-3 h-3 border-2 border-white transition-all duration-200
          ${selected ? '!bg-blue-600 scale-125' : '!bg-blue-500'}
        `}
      />

      <Card
        data-testid="paper-node-card"
        className={`
          w-64 sm:w-56 md:w-64 lg:w-72 min-h-32 transition-all duration-300 backdrop-blur-sm
          ${selected
            ? 'ring-2 ring-blue-500 shadow-2xl bg-blue-50/90 border-blue-300'
            : hovered
              ? 'shadow-xl bg-white/95 border-blue-200'
              : 'shadow-lg bg-white/90 border-gray-200'
          }
        `}
      >
        <CardHeader className="pb-2">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-2">
              <motion.div
                animate={{ rotate: selected ? 360 : 0 }}
                transition={{ duration: 0.5 }}
              >
                <BookOpen className={`h-4 w-4 ${selected ? 'text-blue-700' : 'text-blue-600'}`} />
              </motion.div>
              <Badge
                variant={selected ? "default" : "secondary"}
                className={`text-xs ${selected ? 'bg-blue-600' : ''}`}
              >
                Paper
              </Badge>
            </div>
            {relationshipCount && relationshipCount > 0 && (
              <Badge
                variant="outline"
                className={`text-xs transition-colors duration-200 ${
                  selected ? 'border-blue-500 text-blue-700' : ''
                }`}
              >
                {relationshipCount} links
              </Badge>
            )}
          </div>

          <h3 className={`
            text-sm font-semibold leading-tight line-clamp-2 transition-colors duration-200
            ${selected ? 'text-blue-900' : 'text-gray-900'}
          `}>
            {title}
          </h3>
        </CardHeader>
        
        <CardContent className="pt-0 space-y-2">
          {authors && (
            <div className="flex items-center space-x-1 text-xs text-gray-600">
              <Users className="h-3 w-3" />
              <span className="truncate">{authors}</span>
            </div>
          )}
          
          {year && (
            <div className="flex items-center space-x-1 text-xs text-gray-600">
              <Calendar className="h-3 w-3" />
              <span>{year}</span>
            </div>
          )}
          
          <div className="flex items-center justify-between">
            <div className="flex space-x-1">
              {notes && (
                <MessageSquare className="h-3 w-3 text-gray-400" />
              )}
              {link && (
                <ExternalLink className="h-3 w-3 text-gray-400" />
              )}
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Handle
        type="source"
        position={Position.Bottom}
        className={`
          w-3 h-3 border-2 border-white transition-all duration-200
          ${selected ? '!bg-green-600 scale-125' : '!bg-green-500'}
        `}
      />
    </motion.div>
  );
});

PaperNode.displayName = 'PaperNode';

export default PaperNode;
