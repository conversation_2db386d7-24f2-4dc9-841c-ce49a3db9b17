"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { ReactFlowProvider } from 'reactflow';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  PanelLeft, 
  PanelRight, 
  PanelBottom,
  Settings,
  Search,
  Filter,
  Plus,
  BarChart3,
  Network,
  FileText,
  HelpCircle,
  Maximize2,
  Minimize2
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

import { ComprehensiveGraphCanvas } from '@/components/comprehensive/graph-canvas';
import { LeftControlPanel } from '@/components/comprehensive/left-panel';
import { RightDetailsPanel } from '@/components/comprehensive/right-panel';
import { BottomStatsPanel } from '@/components/comprehensive/bottom-panel';
import { TopToolbar } from '@/components/comprehensive/top-toolbar';

interface ComprehensiveDashboardProps {
  projectId: string;
}

interface PanelState {
  left: boolean;
  right: boolean;
  bottom: boolean;
}

export function ComprehensiveDashboard({ projectId }: ComprehensiveDashboardProps) {
  // Panel visibility state
  const [panels, setPanels] = useState<PanelState>({
    left: true,
    right: true,
    bottom: false
  });

  // Dashboard mode state
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [activeMode, setActiveMode] = useState<'explore' | 'edit' | 'analyze'>('explore');

  // Toggle panel visibility
  const togglePanel = useCallback((panel: keyof PanelState) => {
    setPanels(prev => ({
      ...prev,
      [panel]: !prev[panel]
    }));
  }, []);

  // Toggle fullscreen mode
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
    if (!isFullscreen) {
      // Hide all panels in fullscreen
      setPanels({ left: false, right: false, bottom: false });
    } else {
      // Restore default panel state
      setPanels({ left: true, right: true, bottom: false });
    }
  }, [isFullscreen]);

  // Calculate panel dimensions
  const leftPanelWidth = panels.left ? 320 : 0;
  const rightPanelWidth = panels.right ? 360 : 0;
  const bottomPanelHeight = panels.bottom ? 200 : 0;

  return (
    <ReactFlowProvider>
      <div className="h-full w-full bg-gray-50 relative overflow-hidden">
        {/* Top Toolbar */}
        <TopToolbar
          projectId={projectId}
          activeMode={activeMode}
          onModeChange={setActiveMode}
          isFullscreen={isFullscreen}
          onToggleFullscreen={toggleFullscreen}
          panels={panels}
          onTogglePanel={togglePanel}
        />

        {/* Main Content Area */}
        <div className="flex h-[calc(100%-60px)] relative">
          {/* Left Control Panel */}
          <AnimatePresence>
            {panels.left && (
              <motion.div
                initial={{ x: -leftPanelWidth, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: -leftPanelWidth, opacity: 0 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="absolute left-0 top-0 h-full z-20 bg-white border-r border-gray-200 shadow-lg"
                style={{ width: leftPanelWidth }}
              >
                <LeftControlPanel 
                  projectId={projectId}
                  activeMode={activeMode}
                  onClose={() => togglePanel('left')}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Central Graph Canvas */}
          <div 
            className="flex-1 relative"
            style={{
              marginLeft: panels.left ? leftPanelWidth : 0,
              marginRight: panels.right ? rightPanelWidth : 0,
              marginBottom: panels.bottom ? bottomPanelHeight : 0,
            }}
          >
            <ComprehensiveGraphCanvas 
              projectId={projectId}
              activeMode={activeMode}
              isFullscreen={isFullscreen}
            />
          </div>

          {/* Right Details Panel */}
          <AnimatePresence>
            {panels.right && (
              <motion.div
                initial={{ x: rightPanelWidth, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: rightPanelWidth, opacity: 0 }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="absolute right-0 top-0 h-full z-20 bg-white border-l border-gray-200 shadow-lg"
                style={{ width: rightPanelWidth }}
              >
                <RightDetailsPanel 
                  projectId={projectId}
                  activeMode={activeMode}
                  onClose={() => togglePanel('right')}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Bottom Stats Panel */}
        <AnimatePresence>
          {panels.bottom && (
            <motion.div
              initial={{ y: bottomPanelHeight, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: bottomPanelHeight, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="absolute bottom-0 left-0 right-0 z-20 bg-white border-t border-gray-200 shadow-lg"
              style={{ 
                height: bottomPanelHeight,
                marginLeft: panels.left ? leftPanelWidth : 0,
                marginRight: panels.right ? rightPanelWidth : 0,
              }}
            >
              <BottomStatsPanel 
                projectId={projectId}
                onClose={() => togglePanel('bottom')}
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Panel Toggle Buttons (when panels are hidden) */}
        {!panels.left && (
          <Button
            variant="outline"
            size="sm"
            className="absolute left-4 top-20 z-30 bg-white/90 backdrop-blur-sm shadow-lg"
            onClick={() => togglePanel('left')}
          >
            <PanelLeft className="h-4 w-4" />
          </Button>
        )}

        {!panels.right && (
          <Button
            variant="outline"
            size="sm"
            className="absolute right-4 top-20 z-30 bg-white/90 backdrop-blur-sm shadow-lg"
            onClick={() => togglePanel('right')}
          >
            <PanelRight className="h-4 w-4" />
          </Button>
        )}

        {!panels.bottom && (
          <Button
            variant="outline"
            size="sm"
            className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-30 bg-white/90 backdrop-blur-sm shadow-lg"
            onClick={() => togglePanel('bottom')}
          >
            <PanelBottom className="h-4 w-4" />
          </Button>
        )}
      </div>
    </ReactFlowProvider>
  );
}
