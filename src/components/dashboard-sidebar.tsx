"use client";

import Link from "next/link";
import { useProjectStore } from "@/store/projectStore";
import { Menu } from "antd";
import { usePathname } from 'next/navigation';

interface DashboardSidebarProps {
  projectId: string;
}

export function DashboardSidebar({ projectId }: DashboardSidebarProps) {
  const pathname = usePathname();

  const links = [
    { key: "comprehensive", label: "Dashboard", href: `/dashboard/${projectId}/comprehensive` },
    { key: "papers", label: "Papers", href: `/dashboard/${projectId}/papers` },
    { key: "visual-map", label: "Visual Map", href: `/dashboard/${projectId}/visual-map` },
    { key: "relationships", label: "Relationships", href: `/dashboard/${projectId}/relationships` },
    { key: "questions", label: "Research Questions", href: `/dashboard/${projectId}/questions` },
  ];

  const selectedKey = links.find(link => pathname.startsWith(link.href))?.key || 'papers';

  return (
    <Menu
      mode="inline"
      selectedKeys={[selectedKey]}
      style={{ height: '100%', borderRight: 0 }}
    >
      {links.map((link) => (
        <Menu.Item key={link.key}>
          <Link href={link.href}>{link.label}</Link>
        </Menu.Item>
      ))}
    </Menu>
  );
}