"use client";

import { usePaperStore } from "@/store/paperStore";
import { <PERSON>ton, Card, Typography, Spin, Alert, Space } from "antd";
import Link from "next/link";
import { useEffect } from "react";

const { Title, Text } = Typography;

interface PaperDetailViewProps {
  projectId: string;
  paperId: string;
}

export function PaperDetailView({ projectId, paperId }: PaperDetailViewProps) {
  const { selectedPaper, isLoading, error, getPaper } = usePaperStore();

  useEffect(() => {
    if (!selectedPaper || selectedPaper.id !== paperId) {
      getPaper(projectId, paperId);
    }
  }, [projectId, paperId, selectedPaper, getPaper]);

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <Spin size="large" tip="Loading paper details..." />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Error"
        description={error}
        type="error"
        showIcon
        style={{ marginBottom: '20px' }}
      />
    );
  }

  if (!selectedPaper) {
    return <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>Paper not found.</div>;
  }

  return (
    <Card
      title={selectedPaper.title}
      extra={
        <Space>
          <Link href={`/dashboard/${projectId}/papers/${paperId}/edit`} passHref>
            <Button type="primary">Edit Paper</Button>
          </Link>
          <Link href={`/dashboard/${projectId}/papers`} passHref>
            <Button>Back to Papers</Button>
          </Link>
        </Space>
      }
      style={{ width: '100%', maxWidth: '800px', margin: '0 auto' }}
    >
      <Text strong>Authors: <AUTHORS>
      <Text strong>Publication Year:</Text> <Text>{selectedPaper.publicationYear || 'N/A'}</Text><br />
      {selectedPaper.link && (
        <>
          <Text strong>Link:</Text> <a href={selectedPaper.link} target="_blank" rel="noopener noreferrer">
            {selectedPaper.link}
          </a><br />
        </>
      )}
      {selectedPaper.notes && (
        <>
          <Text strong>Notes:</Text> <Text>{selectedPaper.notes}</Text><br />
        </>
      )}
    </Card>
  );
}