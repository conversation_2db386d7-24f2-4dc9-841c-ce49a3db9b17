"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Eye,
  EyeOff,
  Layers,
  FileText,
  GitBranch,
  HelpCircle,
  Calendar,
  User,
  Tag
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';

interface LayerControlsProps {
  projectId: string;
}

interface Layer {
  key: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  visible: boolean;
  opacity: number;
  color: string;
}

export function LayerControls({ projectId }: LayerControlsProps) {
  const [layers, setLayers] = useState<Layer[]>([
    {
      key: 'papers',
      label: 'Papers',
      icon: FileText,
      visible: true,
      opacity: 100,
      color: 'text-blue-600'
    },
    {
      key: 'relationships',
      label: 'Relationships',
      icon: GitBranch,
      visible: true,
      opacity: 100,
      color: 'text-green-600'
    },
    {
      key: 'questions',
      label: 'Questions',
      icon: HelpCircle,
      visible: false,
      opacity: 80,
      color: 'text-purple-600'
    },
    {
      key: 'timeline',
      label: 'Timeline',
      icon: Calendar,
      visible: false,
      opacity: 60,
      color: 'text-orange-600'
    },
    {
      key: 'authors',
      label: 'Authors',
      icon: User,
      visible: false,
      opacity: 70,
      color: 'text-indigo-600'
    },
    {
      key: 'tags',
      label: 'Tags',
      icon: Tag,
      visible: false,
      opacity: 50,
      color: 'text-pink-600'
    }
  ]);

  const toggleLayerVisibility = (layerKey: string) => {
    setLayers(prev => prev.map(layer => 
      layer.key === layerKey 
        ? { ...layer, visible: !layer.visible }
        : layer
    ));
  };

  const updateLayerOpacity = (layerKey: string, opacity: number) => {
    setLayers(prev => prev.map(layer => 
      layer.key === layerKey 
        ? { ...layer, opacity }
        : layer
    ));
  };

  const visibleLayers = layers.filter(layer => layer.visible);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">Layers & Views</h4>
        <Badge variant="outline" className="text-xs">
          {visibleLayers.length}/{layers.length} visible
        </Badge>
      </div>

      <div className="space-y-3">
        {layers.map((layer) => {
          const Icon = layer.icon;
          
          return (
            <motion.div
              key={layer.key}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className={`
                p-3 rounded-lg border transition-all duration-200
                ${layer.visible 
                  ? 'bg-white border-gray-200 shadow-sm' 
                  : 'bg-gray-50 border-gray-100'
                }
              `}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Icon className={`h-4 w-4 ${layer.color}`} />
                  <span className={`text-sm font-medium ${
                    layer.visible ? 'text-gray-800' : 'text-gray-500'
                  }`}>
                    {layer.label}
                  </span>
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleLayerVisibility(layer.key)}
                  className={`h-6 w-6 p-0 ${
                    layer.visible ? layer.color : 'text-gray-400'
                  }`}
                >
                  {layer.visible ? (
                    <Eye className="h-3 w-3" />
                  ) : (
                    <EyeOff className="h-3 w-3" />
                  )}
                </Button>
              </div>

              {layer.visible && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-2"
                >
                  <div className="flex items-center justify-between text-xs text-gray-600">
                    <span>Opacity</span>
                    <span>{layer.opacity}%</span>
                  </div>
                  
                  <Slider
                    value={[layer.opacity]}
                    onValueChange={([value]) => updateLayerOpacity(layer.key, value)}
                    max={100}
                    step={10}
                    className="w-full"
                  />
                </motion.div>
              )}
            </motion.div>
          );
        })}
      </div>

      <div className="pt-2 border-t border-gray-200">
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setLayers(prev => prev.map(layer => ({ ...layer, visible: true })))}
            className="flex-1 text-xs"
          >
            Show All
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setLayers(prev => prev.map(layer => ({ ...layer, visible: false })))}
            className="flex-1 text-xs"
          >
            Hide All
          </Button>
        </div>
      </div>
    </div>
  );
}
