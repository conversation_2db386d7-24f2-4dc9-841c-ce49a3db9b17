"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  X,
  FileText,
  GitBranch,
  HelpCircle,
  Info,
  Edit3,
  Trash2,
  ExternalLink,
  Calendar,
  User,
  Tag,
  ChevronDown,
  ChevronRight
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';

import { useGraphStore } from '@/store/graphStore';
import { usePaperStore } from '@/store/paperStore';
import { useRelationshipStore } from '@/store/relationshipStore';

interface RightDetailsPanelProps {
  projectId: string;
  activeMode: 'explore' | 'edit' | 'analyze';
  onClose: () => void;
}

export function RightDetailsPanel({ projectId, activeMode, onClose }: RightDetailsPanelProps) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    selection: true,
    details: true,
    actions: activeMode === 'edit',
    insights: activeMode === 'analyze',
  });

  const { selectedNodes, selectedEdges } = useGraphStore();
  const { papers } = usePaperStore();
  const { relationships } = useRelationshipStore();

  const toggleSection = (key: string) => {
    setOpenSections(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Get selected items data
  const selectedPapers = papers.filter(paper => selectedNodes.includes(paper.id));
  const selectedRelationships = relationships.filter(rel => selectedEdges.includes(rel.id));

  const hasSelection = selectedNodes.length > 0 || selectedEdges.length > 0;

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <h2 className="font-semibold text-gray-800">Details</h2>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {!hasSelection ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Info className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="font-medium text-gray-800 mb-2">No Selection</h3>
              <p className="text-sm text-gray-600">
                Select papers or relationships to view details and perform actions.
              </p>
            </div>
          ) : (
            <>
              {/* Selection Summary */}
              <Collapsible
                open={openSections.selection}
                onOpenChange={() => toggleSection('selection')}
              >
                <CollapsibleTrigger asChild>
                  <Button
                    variant="ghost"
                    className="w-full justify-between p-3 h-auto hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-2">
                      <FileText className="h-4 w-4 text-gray-600" />
                      <span className="font-medium text-gray-800">Selection</span>
                    </div>
                    {openSections.selection ? (
                      <ChevronDown className="h-4 w-4 text-gray-400" />
                    ) : (
                      <ChevronRight className="h-4 w-4 text-gray-400" />
                    )}
                  </Button>
                </CollapsibleTrigger>
                
                <CollapsibleContent className="mt-2">
                  <div className="p-3 bg-gray-50 rounded-lg space-y-3">
                    {selectedPapers.length > 0 && (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700">Papers</span>
                          <Badge variant="outline">{selectedPapers.length}</Badge>
                        </div>
                        <div className="space-y-1">
                          {selectedPapers.slice(0, 3).map(paper => (
                            <div key={paper.id} className="text-sm text-gray-600 truncate">
                              {paper.title}
                            </div>
                          ))}
                          {selectedPapers.length > 3 && (
                            <div className="text-xs text-gray-500">
                              +{selectedPapers.length - 3} more...
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {selectedRelationships.length > 0 && (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700">Relationships</span>
                          <Badge variant="outline">{selectedRelationships.length}</Badge>
                        </div>
                        <div className="space-y-1">
                          {selectedRelationships.slice(0, 3).map(rel => (
                            <div key={rel.id} className="text-sm text-gray-600">
                              {rel.type.toLowerCase().replace('_', ' ')}
                            </div>
                          ))}
                          {selectedRelationships.length > 3 && (
                            <div className="text-xs text-gray-500">
                              +{selectedRelationships.length - 3} more...
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </CollapsibleContent>
              </Collapsible>

              {/* Detailed Information */}
              {selectedPapers.length === 1 && (
                <Collapsible
                  open={openSections.details}
                  onOpenChange={() => toggleSection('details')}
                >
                  <CollapsibleTrigger asChild>
                    <Button
                      variant="ghost"
                      className="w-full justify-between p-3 h-auto hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-2">
                        <Info className="h-4 w-4 text-gray-600" />
                        <span className="font-medium text-gray-800">Paper Details</span>
                      </div>
                      {openSections.details ? (
                        <ChevronDown className="h-4 w-4 text-gray-400" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent className="mt-2">
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">{selectedPapers[0].title}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {selectedPapers[0].authors && (
                          <div className="flex items-start space-x-2">
                            <User className="h-4 w-4 text-gray-500 mt-0.5" />
                            <div>
                              <div className="text-sm font-medium text-gray-700">Authors</div>
                              <div className="text-sm text-gray-600">{selectedPapers[0].authors}</div>
                            </div>
                          </div>
                        )}

                        {selectedPapers[0].publicationYear && (
                          <div className="flex items-start space-x-2">
                            <Calendar className="h-4 w-4 text-gray-500 mt-0.5" />
                            <div>
                              <div className="text-sm font-medium text-gray-700">Year</div>
                              <div className="text-sm text-gray-600">{selectedPapers[0].publicationYear}</div>
                            </div>
                          </div>
                        )}

                        {selectedPapers[0].link && (
                          <div className="flex items-start space-x-2">
                            <ExternalLink className="h-4 w-4 text-gray-500 mt-0.5" />
                            <div>
                              <div className="text-sm font-medium text-gray-700">Link</div>
                              <a 
                                href={selectedPapers[0].link} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-sm text-blue-600 hover:text-blue-800 underline"
                              >
                                View Paper
                              </a>
                            </div>
                          </div>
                        )}

                        {selectedPapers[0].notes && (
                          <div className="flex items-start space-x-2">
                            <FileText className="h-4 w-4 text-gray-500 mt-0.5" />
                            <div>
                              <div className="text-sm font-medium text-gray-700">Notes</div>
                              <div className="text-sm text-gray-600">{selectedPapers[0].notes}</div>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </CollapsibleContent>
                </Collapsible>
              )}

              {/* Actions */}
              {activeMode === 'edit' && (
                <Collapsible
                  open={openSections.actions}
                  onOpenChange={() => toggleSection('actions')}
                >
                  <CollapsibleTrigger asChild>
                    <Button
                      variant="ghost"
                      className="w-full justify-between p-3 h-auto hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-2">
                        <Edit3 className="h-4 w-4 text-gray-600" />
                        <span className="font-medium text-gray-800">Actions</span>
                      </div>
                      {openSections.actions ? (
                        <ChevronDown className="h-4 w-4 text-gray-400" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                  
                  <CollapsibleContent className="mt-2">
                    <div className="space-y-2">
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <Edit3 className="h-4 w-4 mr-2" />
                        Edit Selected
                      </Button>
                      
                      <Button variant="outline" size="sm" className="w-full justify-start">
                        <GitBranch className="h-4 w-4 mr-2" />
                        Create Relationship
                      </Button>
                      
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete Selected
                      </Button>
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              )}
            </>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
