"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { 
  X,
  Edit3,
  Trash2,
  GitBranch,
  Copy,
  Group,
  Ungroup,
  Eye,
  EyeOff,
  BarChart3
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

interface SelectionToolbarProps {
  projectId: string;
  selectedNodes: string[];
  selectedEdges: string[];
  activeMode: 'explore' | 'edit' | 'analyze';
  onClearSelection: () => void;
}

export function SelectionToolbar({
  projectId,
  selectedNodes,
  selectedEdges,
  activeMode,
  onClearSelection
}: SelectionToolbarProps) {
  const totalSelected = selectedNodes.length + selectedEdges.length;
  const hasNodes = selectedNodes.length > 0;
  const hasEdges = selectedEdges.length > 0;
  const hasMultiple = totalSelected > 1;

  const handleEdit = () => {
    console.log('Edit selected items:', { nodes: selectedNodes, edges: selectedEdges });
    // TODO: Implement edit functionality
  };

  const handleDelete = () => {
    console.log('Delete selected items:', { nodes: selectedNodes, edges: selectedEdges });
    // TODO: Implement delete functionality
  };

  const handleCreateRelationship = () => {
    console.log('Create relationship between selected nodes:', selectedNodes);
    // TODO: Implement relationship creation
  };

  const handleGroup = () => {
    console.log('Group selected items:', { nodes: selectedNodes, edges: selectedEdges });
    // TODO: Implement grouping functionality
  };

  const handleCopy = () => {
    console.log('Copy selected items:', { nodes: selectedNodes, edges: selectedEdges });
    // TODO: Implement copy functionality
  };

  const handleAnalyze = () => {
    console.log('Analyze selected items:', { nodes: selectedNodes, edges: selectedEdges });
    // TODO: Implement analysis functionality
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -20, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.9 }}
      transition={{ duration: 0.2 }}
      className="bg-white border border-gray-200 rounded-lg shadow-lg p-3"
    >
      <div className="flex items-center space-x-3">
        {/* Selection Info */}
        <div className="flex items-center space-x-2">
          <Badge variant="default" className="bg-blue-600">
            {totalSelected} selected
          </Badge>
          
          {hasNodes && (
            <Badge variant="outline" className="text-xs">
              {selectedNodes.length} paper{selectedNodes.length !== 1 ? 's' : ''}
            </Badge>
          )}
          
          {hasEdges && (
            <Badge variant="outline" className="text-xs">
              {selectedEdges.length} link{selectedEdges.length !== 1 ? 's' : ''}
            </Badge>
          )}
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Actions based on mode */}
        <div className="flex items-center space-x-1">
          {activeMode === 'explore' && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopy}
                className="h-8"
                title="Copy selection"
              >
                <Copy className="h-4 w-4" />
              </Button>
              
              {hasNodes && hasMultiple && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleGroup}
                  className="h-8"
                  title="Group items"
                >
                  <Group className="h-4 w-4" />
                </Button>
              )}
            </>
          )}

          {activeMode === 'edit' && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleEdit}
                className="h-8"
                title="Edit selected"
              >
                <Edit3 className="h-4 w-4" />
              </Button>
              
              {hasNodes && selectedNodes.length >= 2 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCreateRelationship}
                  className="h-8"
                  title="Create relationship"
                >
                  <GitBranch className="h-4 w-4" />
                </Button>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopy}
                className="h-8"
                title="Duplicate"
              >
                <Copy className="h-4 w-4" />
              </Button>
              
              <Separator orientation="vertical" className="h-6" />
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                className="h-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                title="Delete selected"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </>
          )}

          {activeMode === 'analyze' && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleAnalyze}
                className="h-8"
                title="Analyze selection"
              >
                <BarChart3 className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopy}
                className="h-8"
                title="Export analysis"
              >
                <Copy className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Clear Selection */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearSelection}
          className="h-8 text-gray-500 hover:text-gray-700"
          title="Clear selection"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Additional Info */}
      {activeMode === 'analyze' && totalSelected > 0 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mt-2 pt-2 border-t border-gray-100"
        >
          <div className="flex items-center justify-between text-xs text-gray-600">
            <span>Quick Analysis:</span>
            <div className="flex space-x-3">
              {hasNodes && (
                <span>Avg. connections: {(Math.random() * 5).toFixed(1)}</span>
              )}
              {hasEdges && (
                <span>Relationship strength: High</span>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}
