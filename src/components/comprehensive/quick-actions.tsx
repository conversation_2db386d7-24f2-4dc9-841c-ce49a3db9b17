"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Plus,
  FileText,
  GitBranch,
  HelpCircle,
  Upload,
  Download,
  Trash2
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface QuickActionsProps {
  projectId: string;
  activeMode: 'explore' | 'edit' | 'analyze';
}

export function QuickActions({ projectId, activeMode }: QuickActionsProps) {
  const actions = [
    {
      key: 'add-paper',
      label: 'Add Paper',
      icon: FileText,
      description: 'Add a new research paper',
      color: 'bg-blue-500 hover:bg-blue-600',
      enabled: activeMode === 'edit'
    },
    {
      key: 'add-relationship',
      label: 'Add Link',
      icon: GitBranch,
      description: 'Create relationship between papers',
      color: 'bg-green-500 hover:bg-green-600',
      enabled: activeMode === 'edit'
    },
    {
      key: 'add-question',
      label: 'Add Question',
      icon: HelpCircle,
      description: 'Add research question',
      color: 'bg-purple-500 hover:bg-purple-600',
      enabled: activeMode === 'edit'
    },
    {
      key: 'import',
      label: 'Import',
      icon: Upload,
      description: 'Import research data',
      color: 'bg-orange-500 hover:bg-orange-600',
      enabled: true
    },
    {
      key: 'export',
      label: 'Export',
      icon: Download,
      description: 'Export current view',
      color: 'bg-indigo-500 hover:bg-indigo-600',
      enabled: true
    }
  ];

  const enabledActions = actions.filter(action => action.enabled);

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">Quick Actions</h4>
        <Badge variant="outline" className="text-xs">
          {enabledActions.length} available
        </Badge>
      </div>

      <div className="grid grid-cols-1 gap-2">
        {enabledActions.map(({ key, label, icon: Icon, description, color }) => (
          <motion.div key={key} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              variant="outline"
              size="sm"
              className={`
                w-full justify-start space-x-2 h-auto py-3 transition-all duration-200
                hover:shadow-md hover:border-transparent ${color} hover:text-white
              `}
              title={description}
            >
              <Icon className="h-4 w-4" />
              <div className="flex flex-col items-start">
                <span className="font-medium">{label}</span>
                <span className="text-xs opacity-75">{description}</span>
              </div>
            </Button>
          </motion.div>
        ))}
      </div>

      {activeMode !== 'edit' && (
        <div className="p-3 bg-amber-50 rounded-lg border border-amber-200">
          <p className="text-xs text-amber-700">
            Switch to <strong>Edit mode</strong> to access creation tools.
          </p>
        </div>
      )}
    </div>
  );
}
