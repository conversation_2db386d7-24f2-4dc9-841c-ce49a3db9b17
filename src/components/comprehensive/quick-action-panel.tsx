"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus,
  FileText,
  GitBranch,
  HelpCircle,
  Zap,
  ChevronUp,
  ChevronDown
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface QuickActionPanelProps {
  projectId: string;
  activeMode: 'explore' | 'edit' | 'analyze';
}

export function QuickActionPanel({ projectId, activeMode }: QuickActionPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const quickActions = [
    {
      key: 'add-paper',
      label: 'Add Paper',
      icon: FileText,
      description: 'Quickly add a new research paper',
      color: 'bg-blue-500 hover:bg-blue-600',
      shortcut: 'P'
    },
    {
      key: 'add-relationship',
      label: 'Link Papers',
      icon: GitBranch,
      description: 'Create relationship between papers',
      color: 'bg-green-500 hover:bg-green-600',
      shortcut: 'L'
    },
    {
      key: 'add-question',
      label: 'Add Question',
      icon: HelpCircle,
      description: 'Add research question',
      color: 'bg-purple-500 hover:bg-purple-600',
      shortcut: 'Q'
    }
  ];

  const handleAction = (actionKey: string) => {
    console.log('Quick action:', actionKey);
    // TODO: Implement quick actions
    switch (actionKey) {
      case 'add-paper':
        // Open add paper modal
        break;
      case 'add-relationship':
        // Start relationship creation mode
        break;
      case 'add-question':
        // Open add question modal
        break;
    }
  };

  if (activeMode !== 'edit') {
    return null;
  }

  return (
    <TooltipProvider>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
        className="bg-white border border-gray-200 rounded-lg shadow-lg overflow-hidden"
      >
        {/* Toggle Button */}
        <div className="flex items-center justify-center p-2 bg-gray-50 border-b border-gray-200">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800"
          >
            <Zap className="h-4 w-4" />
            <span className="text-sm font-medium">Quick Actions</span>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronUp className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Collapsed View */}
        {!isExpanded && (
          <div className="flex items-center justify-center p-3 space-x-2">
            {quickActions.map(({ key, icon: Icon, description, color, shortcut }) => (
              <Tooltip key={key}>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAction(key)}
                    className={`
                      h-10 w-10 p-0 transition-all duration-200 hover:scale-105
                      ${color} hover:text-white border-2 hover:border-transparent
                    `}
                  >
                    <Icon className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="text-center">
                    <div className="font-medium">{description}</div>
                    <div className="text-xs text-gray-400 mt-1">
                      Press <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">{shortcut}</kbd>
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            ))}
          </div>
        )}

        {/* Expanded View */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="p-4 space-y-3"
            >
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-gray-800">Available Actions</h3>
                <Badge variant="outline" className="text-xs">
                  Edit Mode
                </Badge>
              </div>

              <div className="grid grid-cols-1 gap-2">
                {quickActions.map(({ key, label, icon: Icon, description, color, shortcut }) => (
                  <motion.div
                    key={key}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      variant="outline"
                      onClick={() => handleAction(key)}
                      className={`
                        w-full justify-start space-x-3 h-auto py-3 transition-all duration-200
                        hover:shadow-md hover:border-transparent ${color} hover:text-white
                      `}
                    >
                      <Icon className="h-5 w-5" />
                      <div className="flex-1 text-left">
                        <div className="font-medium">{label}</div>
                        <div className="text-xs opacity-75">{description}</div>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {shortcut}
                      </Badge>
                    </Button>
                  </motion.div>
                ))}
              </div>

              <div className="pt-2 border-t border-gray-100">
                <p className="text-xs text-gray-500 text-center">
                  💡 Tip: Use keyboard shortcuts for faster access
                </p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </TooltipProvider>
  );
}
