"use client";

import React, { memo, useState } from 'react';
import { EdgeProps, getBezierPath, EdgeLabelRenderer } from 'reactflow';
import { Button } from '@/components/ui/button';
import { GraphEdgeData, relationshipColors } from '@/lib/graph-utils';
import { motion, AnimatePresence } from 'framer-motion';
import { Edit3, Trash2, Info } from 'lucide-react';

interface EnhancedGraphEdgeData extends GraphEdgeData {
  activeMode?: 'explore' | 'edit' | 'analyze';
  isSelected?: boolean;
  isHovered?: boolean;
}

interface EnhancedRelationshipEdgeProps extends EdgeProps<EnhancedGraphEdgeData> {}

export const EnhancedRelationshipEdge = memo(({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  data,
  selected,
}: EnhancedRelationshipEdgeProps) => {
  const [showActions, setShowActions] = useState(false);

  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  const {
    activeMode = 'explore',
    isSelected = false,
    isHovered = false,
  } = data || {};

  const edgeColor = data ? relationshipColors[data.type] : '#6b7280';
  const strokeWidth = isSelected ? 4 : isHovered ? 3 : 2;
  const opacity = isSelected ? 1 : isHovered ? 0.9 : 0.7;

  const handleMouseEnter = () => {
    if (activeMode === 'edit') {
      setShowActions(true);
    }
  };

  const handleMouseLeave = () => {
    setShowActions(false);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implement edit functionality
    console.log('Edit relationship:', id);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implement delete functionality
    console.log('Delete relationship:', id);
  };

  const handleInfo = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Show relationship details
    console.log('Show relationship info:', id);
  };

  return (
    <>
      {/* Edge Path */}
      <motion.path
        id={id}
        style={{
          stroke: edgeColor,
          strokeWidth,
          opacity,
          filter: isSelected ? 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' : 'none',
        }}
        className={`
          react-flow__edge-path transition-all duration-200
          ${activeMode === 'edit' ? 'hover:stroke-width-3' : ''}
        `}
        d={edgePath}
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 0.5, ease: "easeInOut" }}
        markerEnd={`url(#${id}-marker)`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      />
      
      {/* Custom Arrow Marker */}
      <defs>
        <marker
          id={`${id}-marker`}
          markerWidth="12"
          markerHeight="12"
          refX="6"
          refY="3"
          orient="auto"
          markerUnits="strokeWidth"
        >
          <polygon
            points="0,0 0,6 9,3"
            fill={edgeColor}
            opacity={opacity}
          />
        </marker>
      </defs>
      
      {/* Edge Label and Actions */}
      {data && (
        <EdgeLabelRenderer>
          <motion.div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              pointerEvents: 'all',
            }}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ 
              scale: isSelected ? 1.1 : isHovered ? 1.05 : 1,
              opacity: isSelected ? 1 : isHovered ? 0.9 : 0.8,
            }}
            transition={{ duration: 0.2 }}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            className="relative"
          >
            {/* Main Label */}
            <div
              className={`
                px-3 py-1.5 bg-white border rounded-md shadow-sm text-xs font-medium
                transition-all duration-200
                ${isSelected ? 'border-2 shadow-md' : 'border'}
                ${activeMode === 'edit' ? 'hover:shadow-md' : ''}
              `}
              style={{
                borderColor: edgeColor,
                color: edgeColor,
              }}
            >
              {data.label}
              
              {/* Mode Indicator */}
              {activeMode !== 'explore' && (
                <div className={`
                  absolute -top-1 -right-1 w-2 h-2 rounded-full
                  ${activeMode === 'edit' ? 'bg-green-500' : 'bg-purple-500'}
                `} />
              )}
            </div>

            {/* Action Buttons */}
            <AnimatePresence>
              {showActions && activeMode === 'edit' && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8, y: 10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.8, y: 10 }}
                  className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 flex space-x-1 z-10"
                >
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-6 w-6 p-0 bg-white shadow-md"
                    onClick={handleInfo}
                    title="Relationship details"
                  >
                    <Info className="h-3 w-3" />
                  </Button>
                  
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-6 w-6 p-0 bg-white shadow-md"
                    onClick={handleEdit}
                    title="Edit relationship"
                  >
                    <Edit3 className="h-3 w-3" />
                  </Button>
                  
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-6 w-6 p-0 bg-white shadow-md text-red-600 hover:text-red-700"
                    onClick={handleDelete}
                    title="Delete relationship"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Analysis Mode Info */}
            {activeMode === 'analyze' && isSelected && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 z-10"
              >
                <div className="px-2 py-1 bg-purple-100 border border-purple-300 rounded text-xs text-purple-700 whitespace-nowrap">
                  Strength: High • Confidence: 85%
                </div>
              </motion.div>
            )}

            {/* Notes Preview */}
            {data.notes && isHovered && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: 10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 z-10"
              >
                <div className="px-2 py-1 bg-gray-800 text-white text-xs rounded max-w-48 text-center">
                  {data.notes.length > 50 ? `${data.notes.substring(0, 50)}...` : data.notes}
                </div>
              </motion.div>
            )}
          </motion.div>
        </EdgeLabelRenderer>
      )}
    </>
  );
});

EnhancedRelationshipEdge.displayName = 'EnhancedRelationshipEdge';
