"use client";

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  Search,
  Filter,
  X,
  Calendar,
  User,
  Tag,
  SlidersHorizontal
} from 'lucide-react';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { useGraphStore } from '@/store/graphStore';
import { relationshipLabels, relationshipColors } from '@/lib/graph-utils';

interface SearchAndFilterProps {
  projectId: string;
  activeMode: 'explore' | 'edit' | 'analyze';
}

export function SearchAndFilter({ projectId, activeMode }: SearchAndFilterProps) {
  const {
    searchQuery,
    filteredRelationshipTypes,
    yearRange,
    setSearchQuery,
    setRelationshipTypeFilter,
    setYearRange,
  } = useGraphStore();

  const [showAdvanced, setShowAdvanced] = useState(false);
  const [tempYearRange, setTempYearRange] = useState<[number, number]>(
    yearRange || [2000, new Date().getFullYear()]
  );

  const relationshipTypes = Object.entries(relationshipLabels);

  const handleRelationshipFilter = useCallback((type: string) => {
    const newFilters = filteredRelationshipTypes.includes(type)
      ? filteredRelationshipTypes.filter(t => t !== type)
      : [...filteredRelationshipTypes, type];
    setRelationshipTypeFilter(newFilters);
  }, [filteredRelationshipTypes, setRelationshipTypeFilter]);

  const handleYearRangeChange = useCallback((values: number[]) => {
    setTempYearRange([values[0], values[1]]);
  }, []);

  const applyYearRange = useCallback(() => {
    setYearRange(tempYearRange);
  }, [tempYearRange, setYearRange]);

  const clearAllFilters = useCallback(() => {
    setSearchQuery('');
    setRelationshipTypeFilter([]);
    setYearRange(null);
    setTempYearRange([2000, new Date().getFullYear()]);
  }, [setSearchQuery, setRelationshipTypeFilter, setYearRange]);

  const activeFiltersCount = 
    (searchQuery ? 1 : 0) + 
    filteredRelationshipTypes.length + 
    (yearRange ? 1 : 0);

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder="Search papers, authors, keywords..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 pr-10"
        />
        {searchQuery && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSearchQuery('')}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Active Filters Summary */}
      {activeFiltersCount > 0 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="flex items-center justify-between p-2 bg-blue-50 rounded-lg border border-blue-200"
        >
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-700">
              {activeFiltersCount} filter{activeFiltersCount !== 1 ? 's' : ''} active
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-blue-600 hover:text-blue-700 h-6 px-2"
          >
            Clear all
          </Button>
        </motion.div>
      )}

      {/* Relationship Type Filters */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-medium text-gray-700">Relationship Types</h4>
          <Badge variant="outline" className="text-xs">
            {filteredRelationshipTypes.length}/{relationshipTypes.length}
          </Badge>
        </div>
        
        <div className="space-y-2">
          {relationshipTypes.map(([type, label]) => {
            const isActive = filteredRelationshipTypes.includes(type);
            const color = relationshipColors[type as keyof typeof relationshipColors];
            
            return (
              <motion.div
                key={type}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`
                  flex items-center justify-between p-2 rounded-md cursor-pointer transition-all duration-200
                  ${isActive
                    ? 'bg-gray-100 border border-gray-300 shadow-sm'
                    : 'hover:bg-gray-50 border border-transparent'
                  }
                `}
                onClick={() => handleRelationshipFilter(type)}
              >
                <div className="flex items-center space-x-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: color }}
                  />
                  <span className="text-sm font-medium text-gray-700">
                    {label}
                  </span>
                </div>
                {isActive && (
                  <div className="w-2 h-2 bg-blue-600 rounded-full" />
                )}
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Advanced Filters Toggle */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setShowAdvanced(!showAdvanced)}
        className="w-full justify-between"
      >
        <div className="flex items-center space-x-2">
          <SlidersHorizontal className="h-4 w-4" />
          <span>Advanced Filters</span>
        </div>
        <motion.div
          animate={{ rotate: showAdvanced ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <X className="h-4 w-4" />
        </motion.div>
      </Button>

      {/* Advanced Filters */}
      {showAdvanced && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="space-y-4 p-3 bg-gray-50 rounded-lg border"
        >
          {/* Year Range Filter */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium text-gray-700">
                Publication Year
              </label>
              <div className="text-xs text-gray-500">
                {tempYearRange[0]} - {tempYearRange[1]}
              </div>
            </div>
            
            <Slider
              value={tempYearRange}
              onValueChange={handleYearRangeChange}
              min={1990}
              max={new Date().getFullYear()}
              step={1}
              className="mb-2"
            />
            
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={applyYearRange}
                className="flex-1"
              >
                Apply
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setYearRange(null);
                  setTempYearRange([2000, new Date().getFullYear()]);
                }}
                className="flex-1"
              >
                Reset
              </Button>
            </div>
          </div>

          {/* Author Filter */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Author
            </label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Filter by author..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Authors</SelectItem>
                {/* TODO: Populate with actual authors from papers */}
              </SelectContent>
            </Select>
          </div>

          {/* Connection Filter */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Connection Status
            </label>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="All papers..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Papers</SelectItem>
                <SelectItem value="connected">Connected Only</SelectItem>
                <SelectItem value="isolated">Isolated Only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </motion.div>
      )}
    </div>
  );
}
