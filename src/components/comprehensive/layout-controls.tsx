"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { 
  LayoutGrid, 
  Circle, 
  GitBranch, 
  Hand, 
  ZoomIn, 
  ZoomOut, 
  Maximize, 
  RotateCcw,
  Shuffle,
  Target
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { useReactFlow } from 'reactflow';
import { useGraphStore, LayoutType } from '@/store/graphStore';

interface LayoutControlsProps {
  activeMode: 'explore' | 'edit' | 'analyze';
}

export function LayoutControls({ activeMode }: LayoutControlsProps) {
  const { fitView, zoomIn, zoomOut, getZoom } = useReactFlow();
  
  const {
    currentLayout,
    isLayouting,
    setLayout,
    applyLayout,
    refreshLayout,
  } = useGraphStore();

  const layoutOptions = [
    { 
      key: 'force' as LayoutType, 
      label: 'Force', 
      icon: LayoutGrid, 
      description: 'Physics-based automatic layout',
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    { 
      key: 'circular' as LayoutType, 
      label: 'Circular', 
      icon: Circle, 
      description: 'Circular arrangement of nodes',
      color: 'bg-green-500 hover:bg-green-600'
    },
    { 
      key: 'hierarchical' as LayoutType, 
      label: 'Hierarchy', 
      icon: GitBranch, 
      description: 'Tree-like hierarchical structure',
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    { 
      key: 'manual' as LayoutType, 
      label: 'Manual', 
      icon: Hand, 
      description: 'Free positioning and manual arrangement',
      color: 'bg-orange-500 hover:bg-orange-600'
    },
  ];

  const handleLayoutChange = (layout: LayoutType) => {
    setLayout(layout);
    applyLayout(layout);
  };

  const handleRefreshLayout = () => {
    refreshLayout();
  };

  return (
    <div className="space-y-4">
      {/* Layout Selection */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-medium text-gray-700">Layout Algorithm</h4>
          <Badge 
            variant={isLayouting ? "default" : "outline"} 
            className={`text-xs ${isLayouting ? 'animate-pulse' : ''}`}
          >
            {isLayouting ? 'Applying...' : currentLayout}
          </Badge>
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          {layoutOptions.map(({ key, label, icon: Icon, description, color }) => (
            <motion.div key={key} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                variant={currentLayout === key ? "default" : "outline"}
                size="sm"
                onClick={() => handleLayoutChange(key)}
                disabled={isLayouting}
                className={`
                  w-full flex flex-col items-center space-y-1 h-auto py-3 transition-all duration-200
                  ${currentLayout === key 
                    ? `${color} text-white shadow-md` 
                    : 'hover:bg-gray-50 hover:border-gray-300'
                  }
                `}
                title={description}
              >
                <Icon className="h-4 w-4" />
                <span className="text-xs font-medium">{label}</span>
              </Button>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Layout Actions */}
      <div>
        <h4 className="text-sm font-medium text-gray-700 mb-3">Layout Actions</h4>
        
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshLayout}
            disabled={isLayouting}
            className="flex items-center space-x-1"
          >
            <RotateCcw className="h-3 w-3" />
            <span>Refresh</span>
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => applyLayout('force')}
            disabled={isLayouting}
            className="flex items-center space-x-1"
          >
            <Shuffle className="h-3 w-3" />
            <span>Reorganize</span>
          </Button>
        </div>
      </div>

      {/* Zoom Controls */}
      <div>
        <h4 className="text-sm font-medium text-gray-700 mb-3">View Controls</h4>
        
        <div className="space-y-2">
          <div className="grid grid-cols-3 gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => zoomIn()}
              className="flex items-center justify-center"
              title="Zoom In"
            >
              <ZoomIn className="h-3 w-3" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => zoomOut()}
              className="flex items-center justify-center"
              title="Zoom Out"
            >
              <ZoomOut className="h-3 w-3" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => fitView({ duration: 800 })}
              className="flex items-center justify-center"
              title="Fit to View"
            >
              <Maximize className="h-3 w-3" />
            </Button>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => fitView({ duration: 800, padding: 0.1 })}
            className="w-full flex items-center space-x-2"
          >
            <Target className="h-3 w-3" />
            <span>Center All</span>
          </Button>
        </div>
      </div>

      {/* Layout-specific Options */}
      {currentLayout === 'force' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="p-3 bg-blue-50 rounded-lg border border-blue-200"
        >
          <h5 className="text-xs font-medium text-blue-700 mb-2">Force Layout Options</h5>
          <div className="space-y-2">
            <div>
              <label className="text-xs text-blue-600 mb-1 block">Strength</label>
              <Slider
                defaultValue={[50]}
                max={100}
                step={10}
                className="w-full"
              />
            </div>
            <div>
              <label className="text-xs text-blue-600 mb-1 block">Distance</label>
              <Slider
                defaultValue={[150]}
                min={50}
                max={300}
                step={25}
                className="w-full"
              />
            </div>
          </div>
        </motion.div>
      )}

      {currentLayout === 'hierarchical' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="p-3 bg-purple-50 rounded-lg border border-purple-200"
        >
          <h5 className="text-xs font-medium text-purple-700 mb-2">Hierarchy Options</h5>
          <div className="space-y-2">
            <div>
              <label className="text-xs text-purple-600 mb-1 block">Direction</label>
              <div className="grid grid-cols-2 gap-1">
                <Button variant="outline" size="sm" className="text-xs">
                  Top-Down
                </Button>
                <Button variant="ghost" size="sm" className="text-xs">
                  Left-Right
                </Button>
              </div>
            </div>
            <div>
              <label className="text-xs text-purple-600 mb-1 block">Level Spacing</label>
              <Slider
                defaultValue={[100]}
                min={50}
                max={200}
                step={25}
                className="w-full"
              />
            </div>
          </div>
        </motion.div>
      )}

      {activeMode === 'analyze' && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="p-3 bg-amber-50 rounded-lg border border-amber-200"
        >
          <h5 className="text-xs font-medium text-amber-700 mb-2">Analysis Mode</h5>
          <p className="text-xs text-amber-600">
            Layout optimized for pattern analysis and relationship exploration.
          </p>
        </motion.div>
      )}
    </div>
  );
}
