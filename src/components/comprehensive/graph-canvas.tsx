"use client";

import React, { useCallback, useEffect, useMemo } from 'react';
import ReactFlow, {
  Background,
  Controls,
  MiniMap,
  useReactFlow,
  Panel,
  Node,
  Edge,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { useGraphStore } from '@/store/graphStore';
import { usePaperStore } from '@/store/paperStore';
import { useRelationshipStore } from '@/store/relationshipStore';
import { useQuestionStore } from '@/store/questionStore';

// Enhanced node and edge components
import { EnhancedPaperNode } from '@/components/comprehensive/enhanced-paper-node';
import { EnhancedRelationshipEdge } from '@/components/comprehensive/enhanced-relationship-edge';
import { QuickActionPanel } from '@/components/comprehensive/quick-action-panel';
import { SelectionToolbar } from '@/components/comprehensive/selection-toolbar';

import { motion, AnimatePresence } from 'framer-motion';
import { Loader2 } from 'lucide-react';

// Define enhanced node and edge types
const nodeTypes = {
  paperNode: EnhancedPaperNode,
};

const edgeTypes = {
  relationshipEdge: EnhancedRelationshipEdge,
};

interface ComprehensiveGraphCanvasProps {
  projectId: string;
  activeMode: 'explore' | 'edit' | 'analyze';
  isFullscreen: boolean;
}

export function ComprehensiveGraphCanvas({ 
  projectId, 
  activeMode, 
  isFullscreen 
}: ComprehensiveGraphCanvasProps) {
  const { fitView, zoomIn, zoomOut } = useReactFlow();
  
  // Store hooks
  const {
    nodes,
    edges,
    currentLayout,
    isLayouting,
    selectedNodes,
    selectedEdges,
    hoveredNode,
    hoveredEdge,
    showMiniMap,
    showControls,
    showBackground,
    onNodesChange,
    onEdgesChange,
    onConnect,
    selectNode,
    selectEdge,
    clearSelection,
    setHoveredNode,
    setHoveredEdge,
    loadGraphData,
  } = useGraphStore();

  const { papers, fetchPapers, isLoading: papersLoading } = usePaperStore();
  const { relationships, fetchRelationships, isLoading: relationshipsLoading } = useRelationshipStore();
  const { fetchBigQuestions, fetchResearchQuestions } = useQuestionStore();

  // Load data when component mounts
  useEffect(() => {
    if (projectId) {
      fetchPapers(projectId);
      fetchRelationships(projectId);
      fetchBigQuestions(projectId);
      fetchResearchQuestions(projectId);
    }
  }, [projectId, fetchPapers, fetchRelationships, fetchBigQuestions, fetchResearchQuestions]);

  // Update graph data when papers or relationships change
  useEffect(() => {
    if (papers.length > 0 || relationships.length > 0) {
      loadGraphData(papers, relationships);
    }
  }, [papers, relationships, loadGraphData]);

  // Enhanced nodes with mode-specific styling
  const enhancedNodes = useMemo(() => {
    return nodes.map((node) => ({
      ...node,
      data: {
        ...node.data,
        activeMode,
        isSelected: selectedNodes.includes(node.id),
        isHovered: hoveredNode === node.id,
        isFullscreen,
      },
      className: `
        ${selectedNodes.includes(node.id) ? 'selected' : ''}
        ${hoveredNode === node.id ? 'hovered' : ''}
        ${activeMode === 'edit' ? 'editable' : ''}
        ${activeMode === 'analyze' ? 'analyzable' : ''}
      `,
    }));
  }, [nodes, selectedNodes, hoveredNode, activeMode, isFullscreen]);

  // Enhanced edges with mode-specific styling
  const enhancedEdges = useMemo(() => {
    return edges.map((edge) => ({
      ...edge,
      data: {
        ...edge.data,
        activeMode,
        isSelected: selectedEdges.includes(edge.id),
        isHovered: hoveredEdge === edge.id,
      },
      className: `
        ${selectedEdges.includes(edge.id) ? 'selected' : ''}
        ${hoveredEdge === edge.id ? 'hovered' : ''}
        ${activeMode === 'edit' ? 'editable' : ''}
      `,
      animated: activeMode === 'analyze' && selectedEdges.includes(edge.id),
    }));
  }, [edges, selectedEdges, hoveredEdge, activeMode]);

  // Handle node interactions
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    event.stopPropagation();
    selectNode(node.id, event.ctrlKey || event.metaKey);
  }, [selectNode]);

  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    event.stopPropagation();
    selectEdge(edge.id, event.ctrlKey || event.metaKey);
  }, [selectEdge]);

  const onPaneClick = useCallback(() => {
    clearSelection();
  }, [clearSelection]);

  const onNodeMouseEnter = useCallback((_: React.MouseEvent, node: Node) => {
    setHoveredNode(node.id);
  }, [setHoveredNode]);

  const onNodeMouseLeave = useCallback(() => {
    setHoveredNode(null);
  }, [setHoveredNode]);

  const onEdgeMouseEnter = useCallback((_: React.MouseEvent, edge: Edge) => {
    setHoveredEdge(edge.id);
  }, [setHoveredEdge]);

  const onEdgeMouseLeave = useCallback(() => {
    setHoveredEdge(null);
  }, [setHoveredEdge]);

  // Loading state
  if (papersLoading || relationshipsLoading || isLayouting) {
    return (
      <div className="h-full w-full flex items-center justify-center bg-gray-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex flex-col items-center space-y-4"
        >
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-gray-600 font-medium">
            {isLayouting ? 'Applying layout...' : 'Loading research data...'}
          </p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative">
      <ReactFlow
        nodes={enhancedNodes}
        edges={enhancedEdges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClick}
        onEdgeClick={onEdgeClick}
        onPaneClick={onPaneClick}
        onNodeMouseEnter={onNodeMouseEnter}
        onNodeMouseLeave={onNodeMouseLeave}
        onEdgeMouseEnter={onEdgeMouseEnter}
        onEdgeMouseLeave={onEdgeMouseLeave}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        attributionPosition="bottom-left"
        className={`
          bg-gray-50 transition-all duration-300
          ${activeMode === 'edit' ? 'cursor-crosshair' : ''}
          ${isFullscreen ? 'rounded-none' : 'rounded-lg'}
        `}
        minZoom={0.1}
        maxZoom={2}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
      >
        {/* Background */}
        {showBackground && (
          <Background 
            color={activeMode === 'analyze' ? '#e0e7ff' : '#e5e7eb'} 
            gap={20} 
            size={1}
            variant="dots"
          />
        )}
        
        {/* Controls */}
        {showControls && !isFullscreen && <Controls />}
        
        {/* MiniMap */}
        {showMiniMap && !isFullscreen && (
          <MiniMap 
            nodeColor={(node) => {
              if (selectedNodes.includes(node.id)) return '#3b82f6';
              if (hoveredNode === node.id) return '#10b981';
              return '#6b7280';
            }}
            maskColor="rgba(0, 0, 0, 0.1)"
            position="top-right"
          />
        )}

        {/* Selection Toolbar */}
        <AnimatePresence>
          {(selectedNodes.length > 0 || selectedEdges.length > 0) && (
            <Panel position="top-center">
              <SelectionToolbar
                projectId={projectId}
                selectedNodes={selectedNodes}
                selectedEdges={selectedEdges}
                activeMode={activeMode}
                onClearSelection={clearSelection}
              />
            </Panel>
          )}
        </AnimatePresence>

        {/* Quick Action Panel */}
        {activeMode === 'edit' && (
          <Panel position="bottom-center">
            <QuickActionPanel
              projectId={projectId}
              activeMode={activeMode}
            />
          </Panel>
        )}
      </ReactFlow>
    </div>
  );
}
