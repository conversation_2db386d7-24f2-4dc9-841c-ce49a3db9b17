"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Search,
  Filter,
  LayoutGrid,
  Plus,
  FileText,
  GitBranch,
  HelpCircle,
  X,
  ChevronDown,
  ChevronRight,
  Settings,
  Layers,
  Target
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';

import { useGraphStore } from '@/store/graphStore';
import { usePaperStore } from '@/store/paperStore';
import { useRelationshipStore } from '@/store/relationshipStore';
import { useQuestionStore } from '@/store/questionStore';

import { SearchAndFilter } from '@/components/comprehensive/search-filter';
import { LayoutControls } from '@/components/comprehensive/layout-controls';
import { QuickActions } from '@/components/comprehensive/quick-actions';
import { LayerControls } from '@/components/comprehensive/layer-controls';

interface LeftControlPanelProps {
  projectId: string;
  activeMode: 'explore' | 'edit' | 'analyze';
  onClose: () => void;
}

interface PanelSection {
  key: string;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  defaultOpen: boolean;
}

export function LeftControlPanel({ projectId, activeMode, onClose }: LeftControlPanelProps) {
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    search: true,
    layout: true,
    actions: activeMode === 'edit',
    layers: false,
    settings: false,
  });

  const { nodes, edges } = useGraphStore();
  const { papers } = usePaperStore();
  const { relationships } = useRelationshipStore();
  const { bigQuestions, researchQuestions } = useQuestionStore();

  const toggleSection = (key: string) => {
    setOpenSections(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const sections: PanelSection[] = [
    { key: 'search', title: 'Search & Filter', icon: Search, defaultOpen: true },
    { key: 'layout', title: 'Layout Controls', icon: LayoutGrid, defaultOpen: true },
    { key: 'actions', title: 'Quick Actions', icon: Plus, defaultOpen: activeMode === 'edit' },
    { key: 'layers', title: 'Layers & Views', icon: Layers, defaultOpen: false },
    { key: 'settings', title: 'Settings', icon: Settings, defaultOpen: false },
  ];

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
          <h2 className="font-semibold text-gray-800">Controls</h2>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-2 gap-3">
          <div className="text-center p-2 bg-white rounded-lg shadow-sm">
            <div className="text-lg font-bold text-blue-600">{papers.length}</div>
            <div className="text-xs text-gray-600">Papers</div>
          </div>
          <div className="text-center p-2 bg-white rounded-lg shadow-sm">
            <div className="text-lg font-bold text-purple-600">{relationships.length}</div>
            <div className="text-xs text-gray-600">Links</div>
          </div>
        </div>
        
        {bigQuestions.length > 0 && (
          <div className="mt-2 text-center p-2 bg-white rounded-lg shadow-sm">
            <div className="text-sm font-medium text-green-600">
              {bigQuestions.length} Research Questions
            </div>
          </div>
        )}
      </div>

      {/* Scrollable Content */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {sections.map(({ key, title, icon: Icon }) => (
            <Collapsible
              key={key}
              open={openSections[key]}
              onOpenChange={() => toggleSection(key)}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full justify-between p-3 h-auto hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-2">
                    <Icon className="h-4 w-4 text-gray-600" />
                    <span className="font-medium text-gray-800">{title}</span>
                  </div>
                  {openSections[key] ? (
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  ) : (
                    <ChevronRight className="h-4 w-4 text-gray-400" />
                  )}
                </Button>
              </CollapsibleTrigger>
              
              <CollapsibleContent className="mt-2">
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  {key === 'search' && (
                    <SearchAndFilter projectId={projectId} activeMode={activeMode} />
                  )}
                  
                  {key === 'layout' && (
                    <LayoutControls activeMode={activeMode} />
                  )}
                  
                  {key === 'actions' && (
                    <QuickActions projectId={projectId} activeMode={activeMode} />
                  )}
                  
                  {key === 'layers' && (
                    <LayerControls projectId={projectId} />
                  )}
                  
                  {key === 'settings' && (
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-600">
                        Advanced settings and preferences coming soon...
                      </p>
                    </div>
                  )}
                </motion.div>
              </CollapsibleContent>
            </Collapsible>
          ))}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Mode: {activeMode.charAt(0).toUpperCase() + activeMode.slice(1)}</span>
          <Badge variant="outline" className="text-xs">
            {nodes.length} nodes
          </Badge>
        </div>
      </div>
    </div>
  );
}
