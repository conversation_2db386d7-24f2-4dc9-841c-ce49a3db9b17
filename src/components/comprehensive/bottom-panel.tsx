"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { 
  X,
  <PERSON><PERSON><PERSON>3,
  TrendingUp,
  Network,
  Clock,
  Users,
  BookOpen
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

import { useGraphStore } from '@/store/graphStore';
import { usePaperStore } from '@/store/paperStore';
import { useRelationshipStore } from '@/store/relationshipStore';

interface BottomStatsPanelProps {
  projectId: string;
  onClose: () => void;
}

export function BottomStatsPanel({ projectId, onClose }: BottomStatsPanelProps) {
  const { nodes, edges } = useGraphStore();
  const { papers } = usePaperStore();
  const { relationships } = useRelationshipStore();

  // Calculate statistics
  const stats = {
    totalPapers: papers.length,
    totalRelationships: relationships.length,
    connectedPapers: nodes.filter(node => 
      edges.some(edge => edge.source === node.id || edge.target === node.id)
    ).length,
    isolatedPapers: nodes.filter(node => 
      !edges.some(edge => edge.source === node.id || edge.target === node.id)
    ).length,
    avgConnections: papers.length > 0 ? (relationships.length * 2) / papers.length : 0,
    networkDensity: papers.length > 1 ? (relationships.length / ((papers.length * (papers.length - 1)) / 2)) * 100 : 0
  };

  // Calculate year distribution
  const yearDistribution = papers.reduce((acc, paper) => {
    if (paper.publicationYear) {
      acc[paper.publicationYear] = (acc[paper.publicationYear] || 0) + 1;
    }
    return acc;
  }, {} as Record<number, number>);

  const recentYears = Object.entries(yearDistribution)
    .sort(([a], [b]) => parseInt(b) - parseInt(a))
    .slice(0, 5);

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <BarChart3 className="h-5 w-5 text-blue-600" />
          <h2 className="font-semibold text-gray-800">Analytics Dashboard</h2>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 overflow-x-auto">
        <div className="flex space-x-4 min-w-max">
          {/* Overview Stats */}
          <Card className="w-64 flex-shrink-0">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center space-x-2">
                <Network className="h-4 w-4 text-blue-600" />
                <span>Network Overview</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <div className="text-center p-2 bg-blue-50 rounded-lg">
                  <div className="text-lg font-bold text-blue-700">{stats.totalPapers}</div>
                  <div className="text-xs text-blue-600">Papers</div>
                </div>
                <div className="text-center p-2 bg-purple-50 rounded-lg">
                  <div className="text-lg font-bold text-purple-700">{stats.totalRelationships}</div>
                  <div className="text-xs text-purple-600">Links</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">Connected:</span>
                  <Badge variant="secondary">{stats.connectedPapers}</Badge>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">Isolated:</span>
                  <Badge variant="outline">{stats.isolatedPapers}</Badge>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">Avg. Connections:</span>
                  <Badge variant="default">{stats.avgConnections.toFixed(1)}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Network Density */}
          <Card className="w-64 flex-shrink-0">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <span>Network Density</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-700">
                  {stats.networkDensity.toFixed(1)}%
                </div>
                <div className="text-sm text-gray-600">Connection Density</div>
              </div>
              
              <Progress value={stats.networkDensity} className="w-full" />
              
              <div className="text-xs text-gray-500 text-center">
                {stats.networkDensity < 20 ? 'Sparse network' : 
                 stats.networkDensity < 50 ? 'Moderate density' : 
                 'Dense network'}
              </div>
            </CardContent>
          </Card>

          {/* Publication Timeline */}
          <Card className="w-80 flex-shrink-0">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center space-x-2">
                <Clock className="h-4 w-4 text-orange-600" />
                <span>Publication Timeline</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {recentYears.length > 0 ? (
                <div className="space-y-2">
                  {recentYears.map(([year, count]) => (
                    <div key={year} className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">{year}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-orange-500 h-2 rounded-full"
                            style={{ width: `${(count / Math.max(...recentYears.map(([, c]) => c))) * 100}%` }}
                          />
                        </div>
                        <Badge variant="outline" className="text-xs">{count}</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center text-gray-500 text-sm">
                  No publication years available
                </div>
              )}
            </CardContent>
          </Card>

          {/* Research Insights */}
          <Card className="w-64 flex-shrink-0">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center space-x-2">
                <BookOpen className="h-4 w-4 text-purple-600" />
                <span>Research Insights</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2 text-sm">
                <div className="p-2 bg-purple-50 rounded-lg">
                  <div className="font-medium text-purple-700">Most Connected</div>
                  <div className="text-purple-600 text-xs">
                    {/* TODO: Calculate most connected paper */}
                    Analysis coming soon...
                  </div>
                </div>
                
                <div className="p-2 bg-green-50 rounded-lg">
                  <div className="font-medium text-green-700">Key Themes</div>
                  <div className="text-green-600 text-xs">
                    {/* TODO: Extract key themes */}
                    Theme extraction coming soon...
                  </div>
                </div>
                
                <div className="p-2 bg-blue-50 rounded-lg">
                  <div className="font-medium text-blue-700">Research Gaps</div>
                  <div className="text-blue-600 text-xs">
                    {stats.isolatedPapers > 0 
                      ? `${stats.isolatedPapers} isolated papers found`
                      : 'Well-connected research'
                    }
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
