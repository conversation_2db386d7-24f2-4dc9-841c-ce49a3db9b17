"use client";

import React, { memo, useState } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { GraphNodeData } from '@/lib/graph-utils';
import { 
  BookOpen, 
  Users, 
  Calendar, 
  ExternalLink, 
  MessageSquare,
  Edit3,
  Trash2,
  MoreHorizontal,
  Eye,
  GitBranch
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface EnhancedPaperNodeData extends GraphNodeData {
  activeMode?: 'explore' | 'edit' | 'analyze';
  isSelected?: boolean;
  isHovered?: boolean;
  isFullscreen?: boolean;
}

interface EnhancedPaperNodeProps extends NodeProps<EnhancedPaperNodeData> {}

export const EnhancedPaperNode = memo(({ data, selected, id }: EnhancedPaperNodeProps) => {
  const [showActions, setShowActions] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const {
    title,
    authors,
    year,
    relationshipCount,
    notes,
    link,
    activeMode = 'explore',
    isSelected = false,
    isHovered = false,
    isFullscreen = false,
  } = data;

  const nodeWidth = isExpanded ? 320 : isFullscreen ? 280 : 240;
  const nodeHeight = isExpanded ? 'auto' : 'auto';

  const handleMouseEnter = () => {
    if (activeMode === 'edit') {
      setShowActions(true);
    }
  };

  const handleMouseLeave = () => {
    setShowActions(false);
  };

  const handleToggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implement edit functionality
    console.log('Edit paper:', id);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implement delete functionality
    console.log('Delete paper:', id);
  };

  const handleCreateRelationship = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implement relationship creation
    console.log('Create relationship from:', id);
  };

  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ 
        scale: isSelected ? 1.05 : isHovered ? 1.02 : 1,
        opacity: 1 
      }}
      transition={{ duration: 0.2 }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{ width: nodeWidth }}
    >
      <Card 
        className={`
          relative transition-all duration-200 cursor-pointer
          ${isSelected 
            ? 'ring-2 ring-blue-500 shadow-lg border-blue-300' 
            : isHovered 
              ? 'shadow-md border-gray-300' 
              : 'shadow-sm border-gray-200'
          }
          ${activeMode === 'edit' ? 'hover:border-green-300' : ''}
          ${activeMode === 'analyze' ? 'hover:border-purple-300' : ''}
        `}
      >
        {/* Connection Handles */}
        <Handle
          type="target"
          position={Position.Left}
          className="w-3 h-3 bg-blue-500 border-2 border-white"
        />
        <Handle
          type="source"
          position={Position.Right}
          className="w-3 h-3 bg-green-500 border-2 border-white"
        />
        <Handle
          type="source"
          position={Position.Top}
          className="w-3 h-3 bg-purple-500 border-2 border-white"
        />
        <Handle
          type="target"
          position={Position.Bottom}
          className="w-3 h-3 bg-orange-500 border-2 border-white"
        />

        {/* Action Buttons */}
        <AnimatePresence>
          {showActions && activeMode === 'edit' && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="absolute -top-2 -right-2 flex space-x-1 z-10"
            >
              <Button
                size="sm"
                variant="outline"
                className="h-6 w-6 p-0 bg-white shadow-md"
                onClick={handleEdit}
                title="Edit paper"
              >
                <Edit3 className="h-3 w-3" />
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                className="h-6 w-6 p-0 bg-white shadow-md"
                onClick={handleCreateRelationship}
                title="Create relationship"
              >
                <GitBranch className="h-3 w-3" />
              </Button>
              
              <Button
                size="sm"
                variant="outline"
                className="h-6 w-6 p-0 bg-white shadow-md text-red-600 hover:text-red-700"
                onClick={handleDelete}
                title="Delete paper"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Mode Indicator */}
        {activeMode !== 'explore' && (
          <div className={`
            absolute top-2 left-2 w-2 h-2 rounded-full
            ${activeMode === 'edit' ? 'bg-green-500' : 'bg-purple-500'}
          `} />
        )}

        <CardHeader className="pb-2">
          <div className="flex items-start justify-between">
            <h3 className={`
              font-semibold leading-tight
              ${isExpanded ? 'text-sm' : 'text-xs'}
              ${title.length > 60 && !isExpanded ? 'line-clamp-2' : ''}
            `}>
              {title}
            </h3>
            
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 ml-2 flex-shrink-0"
              onClick={handleToggleExpand}
            >
              {isExpanded ? (
                <Eye className="h-3 w-3" />
              ) : (
                <MoreHorizontal className="h-3 w-3" />
              )}
            </Button>
          </div>
        </CardHeader>

        <CardContent className="pt-0 space-y-2">
          {/* Basic Info */}
          <div className="flex items-center justify-between text-xs text-gray-600">
            {authors && (
              <div className="flex items-center space-x-1 flex-1 min-w-0">
                <Users className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">{authors}</span>
              </div>
            )}
            
            {year && (
              <div className="flex items-center space-x-1 flex-shrink-0 ml-2">
                <Calendar className="h-3 w-3" />
                <span>{year}</span>
              </div>
            )}
          </div>

          {/* Relationship Count */}
          {relationshipCount > 0 && (
            <div className="flex items-center justify-between">
              <Badge variant="secondary" className="text-xs">
                <GitBranch className="h-3 w-3 mr-1" />
                {relationshipCount} connection{relationshipCount !== 1 ? 's' : ''}
              </Badge>
              
              {link && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(link, '_blank');
                  }}
                  title="Open paper link"
                >
                  <ExternalLink className="h-3 w-3" />
                </Button>
              )}
            </div>
          )}

          {/* Expanded Content */}
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-2 pt-2 border-t border-gray-100"
              >
                {notes && (
                  <div className="flex items-start space-x-1">
                    <MessageSquare className="h-3 w-3 text-gray-500 mt-0.5 flex-shrink-0" />
                    <p className="text-xs text-gray-600 leading-relaxed">
                      {notes.length > 100 ? `${notes.substring(0, 100)}...` : notes}
                    </p>
                  </div>
                )}
                
                {activeMode === 'analyze' && (
                  <div className="p-2 bg-purple-50 rounded text-xs">
                    <div className="font-medium text-purple-700">Analysis</div>
                    <div className="text-purple-600">
                      Centrality: High • Influence: Medium
                    </div>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </motion.div>
  );
});

EnhancedPaperNode.displayName = 'EnhancedPaperNode';
