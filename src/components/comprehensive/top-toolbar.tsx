"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { 
  PanelLeft, 
  PanelRight, 
  PanelBottom,
  Maximize2,
  Minimize2,
  Eye,
  Edit3,
  Bar<PERSON>hart3,
  Settings,
  Download,
  Upload,
  RefreshCw,
  Zap
} from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface PanelState {
  left: boolean;
  right: boolean;
  bottom: boolean;
}

interface TopToolbarProps {
  projectId: string;
  activeMode: 'explore' | 'edit' | 'analyze';
  onModeChange: (mode: 'explore' | 'edit' | 'analyze') => void;
  isFullscreen: boolean;
  onToggleFullscreen: () => void;
  panels: PanelState;
  onTogglePanel: (panel: keyof PanelState) => void;
}

export function TopToolbar({
  projectId,
  activeMode,
  onModeChange,
  isFullscreen,
  onToggleFullscreen,
  panels,
  onTogglePanel
}: TopToolbarProps) {
  const modes = [
    {
      key: 'explore' as const,
      label: 'Explore',
      icon: Eye,
      description: 'Navigate and explore your research',
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      key: 'edit' as const,
      label: 'Edit',
      icon: Edit3,
      description: 'Edit papers and relationships',
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      key: 'analyze' as const,
      label: 'Analyze',
      icon: BarChart3,
      description: 'Analyze patterns and insights',
      color: 'bg-purple-500 hover:bg-purple-600'
    }
  ];

  return (
    <TooltipProvider>
      <motion.div
        initial={{ y: -60, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="h-15 bg-white border-b border-gray-200 px-4 flex items-center justify-between shadow-sm"
      >
        {/* Left Section - Mode Selector */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
            {modes.map(({ key, label, icon: Icon, description, color }) => (
              <Tooltip key={key}>
                <TooltipTrigger asChild>
                  <Button
                    variant={activeMode === key ? "default" : "ghost"}
                    size="sm"
                    onClick={() => onModeChange(key)}
                    className={`
                      flex items-center space-x-2 transition-all duration-200
                      ${activeMode === key 
                        ? `${color} text-white shadow-md` 
                        : 'hover:bg-white hover:shadow-sm'
                      }
                    `}
                  >
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:inline">{label}</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{description}</p>
                </TooltipContent>
              </Tooltip>
            ))}
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Quick Stats */}
          <div className="hidden md:flex items-center space-x-3 text-sm text-gray-600">
            <Badge variant="outline" className="bg-blue-50 text-blue-700">
              <Zap className="h-3 w-3 mr-1" />
              Live
            </Badge>
          </div>
        </div>

        {/* Center Section - Title */}
        <div className="hidden lg:flex items-center">
          <h1 className="text-lg font-semibold text-gray-800">
            Research Dashboard
          </h1>
        </div>

        {/* Right Section - Controls */}
        <div className="flex items-center space-x-2">
          {/* Panel Toggles */}
          <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={panels.left ? "default" : "ghost"}
                  size="sm"
                  onClick={() => onTogglePanel('left')}
                  className={`
                    transition-all duration-200
                    ${panels.left 
                      ? 'bg-gray-700 text-white shadow-sm' 
                      : 'hover:bg-white hover:shadow-sm'
                    }
                  `}
                >
                  <PanelLeft className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle left panel</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={panels.right ? "default" : "ghost"}
                  size="sm"
                  onClick={() => onTogglePanel('right')}
                  className={`
                    transition-all duration-200
                    ${panels.right 
                      ? 'bg-gray-700 text-white shadow-sm' 
                      : 'hover:bg-white hover:shadow-sm'
                    }
                  `}
                >
                  <PanelRight className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle right panel</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={panels.bottom ? "default" : "ghost"}
                  size="sm"
                  onClick={() => onTogglePanel('bottom')}
                  className={`
                    transition-all duration-200
                    ${panels.bottom 
                      ? 'bg-gray-700 text-white shadow-sm' 
                      : 'hover:bg-white hover:shadow-sm'
                    }
                  `}
                >
                  <PanelBottom className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle bottom panel</p>
              </TooltipContent>
            </Tooltip>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Action Buttons */}
          <div className="flex items-center space-x-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm">
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Refresh data</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Export data</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onToggleFullscreen}
                >
                  {isFullscreen ? (
                    <Minimize2 className="h-4 w-4" />
                  ) : (
                    <Maximize2 className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
      </motion.div>
    </TooltipProvider>
  );
}
