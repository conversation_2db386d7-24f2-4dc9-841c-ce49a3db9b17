"use client";

import React, { useCallback, useEffect, useMemo } from 'react';
import React<PERSON>low, {
  Background,
  Controls,
  MiniMap,
  useReactFlow,
  ReactFlowProvider,
  Panel,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { useGraphStore } from '@/store/graphStore';
import { usePaperStore } from '@/store/paperStore';
import { useRelationshipStore } from '@/store/relationshipStore';
import PaperNode from './graph/PaperNode';
import RelationshipEdge from './graph/RelationshipEdge';
import { GraphControls } from './graph/GraphControls';
import { GraphStats } from './graph/GraphStats';
import { Button } from '@/components/ui/button';
import { ZoomIn, ZoomOut, Maximize } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

// Define node and edge types
const nodeTypes = {
  paperNode: PaperNode,
};

const edgeTypes = {
  relationshipEdge: RelationshipEdge,
};

interface VisualMappingProps {
  projectId: string;
}

function VisualMappingContent({ projectId }: VisualMappingProps) {
  const { fitView, zoomIn, zoomOut } = useReactFlow();
  
  // Store hooks
  const {
    nodes,
    edges,
    currentLayout,
    isLayouting,
    selectedNodes,
    selectedEdges,
    hoveredNode,
    hoveredEdge,
    showMiniMap,
    showControls,
    showBackground,
    onNodesChange,
    onEdgesChange,
    onConnect,
    setLayout,
    applyLayout,
    selectNode,
    selectEdge,
    clearSelection,
    setHoveredNode,
    setHoveredEdge,
    toggleMiniMap,
    toggleControls,
    toggleBackground,
    loadGraphData,
  } = useGraphStore();

  const { papers, fetchPapers } = usePaperStore();
  const { relationships, fetchRelationships } = useRelationshipStore();

  // Load data when component mounts
  useEffect(() => {
    if (projectId) {
      fetchPapers(projectId);
      fetchRelationships(projectId);
    }
  }, [projectId, fetchPapers, fetchRelationships]);

  // Update graph data when papers or relationships change
  useEffect(() => {
    if (papers.length > 0 || relationships.length > 0) {
      loadGraphData(papers, relationships);
    }
  }, [papers, relationships, loadGraphData]);

  // Handle node clicks
  const onNodeClick = useCallback((event: React.MouseEvent, node: any) => {
    event.stopPropagation();
    selectNode(node.id, event.ctrlKey || event.metaKey);
  }, [selectNode]);

  // Handle edge clicks
  const onEdgeClick = useCallback((event: React.MouseEvent, edge: any) => {
    event.stopPropagation();
    selectEdge(edge.id, event.ctrlKey || event.metaKey);
  }, [selectEdge]);

  // Handle background clicks
  const onPaneClick = useCallback(() => {
    clearSelection();
  }, [clearSelection]);

  // Handle node hover
  const onNodeMouseEnter = useCallback((event: React.MouseEvent, node: any) => {
    setHoveredNode(node.id);
  }, [setHoveredNode]);

  const onNodeMouseLeave = useCallback(() => {
    setHoveredNode(null);
  }, [setHoveredNode]);

  // Handle edge hover
  const onEdgeMouseEnter = useCallback((event: React.MouseEvent, edge: any) => {
    setHoveredEdge(edge.id);
  }, [setHoveredEdge]);

  const onEdgeMouseLeave = useCallback(() => {
    setHoveredEdge(null);
  }, [setHoveredEdge]);

  // Enhanced nodes with selection and hover state
  const enhancedNodes = useMemo(() => {
    return nodes.map(node => ({
      ...node,
      data: {
        ...node.data,
        selected: selectedNodes.includes(node.id),
        hovered: hoveredNode === node.id,
      },
    }));
  }, [nodes, selectedNodes, hoveredNode]);

  // Enhanced edges with selection and hover state
  const enhancedEdges = useMemo(() => {
    return edges.map(edge => ({
      ...edge,
      data: {
        ...edge.data,
        selected: selectedEdges.includes(edge.id),
        hovered: hoveredEdge === edge.id,
      },
    }));
  }, [edges, selectedEdges, hoveredEdge]);

  return (
    <div className="w-full h-full relative">
      <ReactFlow
        nodes={enhancedNodes}
        edges={enhancedEdges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClick}
        onEdgeClick={onEdgeClick}
        onPaneClick={onPaneClick}
        onNodeMouseEnter={onNodeMouseEnter}
        onNodeMouseLeave={onNodeMouseLeave}
        onEdgeMouseEnter={onEdgeMouseEnter}
        onEdgeMouseLeave={onEdgeMouseLeave}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        attributionPosition="bottom-left"
        className="bg-gray-50"
      >
        {showBackground && (
          <Background 
            color="#e5e7eb" 
            gap={20} 
            size={1}
            variant="dots"
          />
        )}
        
        {showControls && <Controls />}
        
        {showMiniMap && (
          <MiniMap 
            nodeColor="#3b82f6"
            maskColor="rgba(0, 0, 0, 0.1)"
            position="top-right"
          />
        )}

        {/* Enhanced Controls Panel */}
        <Panel position="top-left" className="hidden sm:block">
          <GraphControls
            onZoomIn={() => zoomIn()}
            onZoomOut={() => zoomOut()}
            onFitView={() => fitView()}
          />
        </Panel>

        {/* Enhanced Stats Panel */}
        <Panel position="top-right" className="hidden md:block">
          <GraphStats />
        </Panel>

        {/* Mobile Controls */}
        <Panel position="bottom-center" className="sm:hidden">
          <div className="flex space-x-2 bg-white/95 backdrop-blur-sm rounded-lg p-2 shadow-lg">
            <Button
              variant="outline"
              size="sm"
              onClick={() => zoomIn()}
              className="flex-1"
            >
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => zoomOut()}
              className="flex-1"
            >
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => fitView()}
              className="flex-1"
            >
              <Maximize className="h-4 w-4" />
            </Button>
          </div>
        </Panel>

        {/* Loading Overlay */}
        <AnimatePresence>
          {isLayouting && (
            <Panel position="center">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="bg-white rounded-lg shadow-lg p-4 flex items-center space-x-3"
              >
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="text-sm font-medium">Applying layout...</span>
              </motion.div>
            </Panel>
          )}
        </AnimatePresence>
      </ReactFlow>
    </div>
  );
}

export function VisualMapping({ projectId }: VisualMappingProps) {
  return (
    <ReactFlowProvider>
      <VisualMappingContent projectId={projectId} />
    </ReactFlowProvider>
  );
}
