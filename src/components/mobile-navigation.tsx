"use client";

import React, { useState } from 'react';
import Link from "next/link";
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Menu, 
  X, 
  FileText, 
  Network, 
  GitBranch, 
  HelpCircle 
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface MobileNavigationProps {
  projectId: string;
}

export function MobileNavigation({ projectId }: MobileNavigationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  const links = [
    { 
      key: "papers", 
      label: "Papers", 
      href: `/dashboard/${projectId}/papers`,
      icon: FileText,
      description: "Manage research papers"
    },
    { 
      key: "visual-map", 
      label: "Visual Map", 
      href: `/dashboard/${projectId}/visual-map`,
      icon: Network,
      description: "Interactive relationship view"
    },
    { 
      key: "relationships", 
      label: "Relationships", 
      href: `/dashboard/${projectId}/relationships`,
      icon: GitBranch,
      description: "Paper connections"
    },
    { 
      key: "questions", 
      label: "Research Questions", 
      href: `/dashboard/${projectId}/questions`,
      icon: HelpCircle,
      description: "Research questions & answers"
    },
  ];

  const selectedKey = links.find(link => pathname.startsWith(link.href))?.key || 'papers';

  const toggleMenu = () => setIsOpen(!isOpen);

  return (
    <div className="lg:hidden">
      {/* Mobile Menu Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={toggleMenu}
        className="fixed top-4 left-4 z-50 bg-white/90 backdrop-blur-sm shadow-md"
      >
        {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </Button>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 z-40"
              onClick={toggleMenu}
            />

            {/* Menu Panel */}
            <motion.div
              initial={{ x: -300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -300, opacity: 0 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
              className="fixed left-0 top-0 h-full w-80 z-50 bg-white shadow-2xl"
            >
              <div className="p-6 pt-16">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">
                  Navigation
                </h2>
                
                <div className="space-y-3">
                  {links.map((link) => {
                    const Icon = link.icon;
                    const isSelected = selectedKey === link.key;
                    
                    return (
                      <Link
                        key={link.key}
                        href={link.href}
                        onClick={toggleMenu}
                        className="block"
                      >
                        <Card 
                          className={`
                            transition-all duration-200 cursor-pointer
                            ${isSelected 
                              ? 'bg-blue-50 border-blue-200 shadow-md' 
                              : 'hover:bg-gray-50 hover:border-gray-300'
                            }
                          `}
                        >
                          <CardContent className="p-4">
                            <div className="flex items-center space-x-3">
                              <Icon 
                                className={`h-5 w-5 ${
                                  isSelected ? 'text-blue-600' : 'text-gray-600'
                                }`} 
                              />
                              <div>
                                <div 
                                  className={`font-medium ${
                                    isSelected ? 'text-blue-900' : 'text-gray-900'
                                  }`}
                                >
                                  {link.label}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {link.description}
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </Link>
                    );
                  })}
                </div>

                {/* Quick Stats */}
                <div className="mt-8 p-4 bg-gray-50 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">
                    Quick Access
                  </h3>
                  <div className="text-xs text-gray-600">
                    Swipe from left edge to open menu
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}
