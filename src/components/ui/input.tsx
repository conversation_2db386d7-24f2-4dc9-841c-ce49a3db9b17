import { Input as AntInput } from "antd";
import * as React from "react";
import { InputRef } from "antd/lib/input";

const Input = React.forwardRef<InputRef, React.ComponentProps<typeof AntInput>>(
  ({ className, type, ...props }, ref) => {
    return (
      <AntInput
        type={type}
        className={className}
        ref={ref}
        {...props}
      />
    );
  }
);
Input.displayName = "Input";

export { Input };
