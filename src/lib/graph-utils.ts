import { Node, Edge } from 'reactflow';
import { RelationshipType } from '@/store/relationshipStore';

// Types for our graph data structures
export interface Paper {
  id: string;
  title: string;
  authors?: string;
  publicationYear?: number;
  link?: string;
  notes?: string;
  projectId: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaperRelationship {
  id: string;
  paperAId: string;
  paperA: Paper;
  paperBId: string;
  paperB: Paper;
  type: RelationshipType;
  notes?: string;
  projectId: string;
}

// Node types for different elements in the graph
export type GraphNodeType = 'paper' | 'question' | 'answer';

export interface GraphNodeData {
  id: string;
  type: GraphNodeType;
  title: string;
  subtitle?: string;
  authors?: string;
  year?: number;
  notes?: string;
  link?: string;
  relationshipCount?: number;
}

export interface GraphEdgeData {
  id: string;
  source: string;
  target: string;
  type: RelationshipType;
  label: string;
  notes?: string;
}

// Color schemes for different relationship types
export const relationshipColors: Record<RelationshipType, string> = {
  'SUPPORTS': '#10b981', // green
  'CONTRADICTS': '#ef4444', // red
  'COMPLEMENTS': '#3b82f6', // blue
  'EXTENDS': '#8b5cf6', // purple
  'IS_EXTENDED_BY': '#f59e0b', // amber
  'USES_METHODOLOGY_OF': '#06b6d4', // cyan
};

export const relationshipLabels: Record<RelationshipType, string> = {
  'SUPPORTS': 'Supports',
  'CONTRADICTS': 'Contradicts',
  'COMPLEMENTS': 'Complements',
  'EXTENDS': 'Extends',
  'IS_EXTENDED_BY': 'Extended By',
  'USES_METHODOLOGY_OF': 'Uses Method',
};

// Transform papers into graph nodes
export function transformPapersToNodes(papers: Paper[]): Node<GraphNodeData>[] {
  return papers.map((paper, index) => ({
    id: paper.id,
    type: 'paperNode',
    position: { 
      x: Math.random() * 800, 
      y: Math.random() * 600 
    },
    data: {
      id: paper.id,
      type: 'paper',
      title: paper.title,
      subtitle: paper.authors,
      authors: paper.authors,
      year: paper.publicationYear,
      notes: paper.notes,
      link: paper.link,
      relationshipCount: 0, // Will be calculated later
    },
    draggable: true,
  }));
}

// Transform relationships into graph edges
export function transformRelationshipsToEdges(relationships: PaperRelationship[]): Edge<GraphEdgeData>[] {
  return relationships.map((relationship) => ({
    id: relationship.id,
    source: relationship.paperAId,
    target: relationship.paperBId,
    type: 'relationshipEdge',
    data: {
      id: relationship.id,
      source: relationship.paperAId,
      target: relationship.paperBId,
      type: relationship.type,
      label: relationshipLabels[relationship.type],
      notes: relationship.notes,
    },
    label: relationshipLabels[relationship.type],
    style: {
      stroke: relationshipColors[relationship.type],
      strokeWidth: 2,
    },
    markerEnd: {
      type: 'arrowclosed',
      color: relationshipColors[relationship.type],
    },
    animated: false,
  }));
}

// Calculate relationship counts for each paper
export function calculateRelationshipCounts(
  papers: Paper[], 
  relationships: PaperRelationship[]
): Record<string, number> {
  const counts: Record<string, number> = {};
  
  papers.forEach(paper => {
    counts[paper.id] = 0;
  });
  
  relationships.forEach(relationship => {
    counts[relationship.paperAId] = (counts[relationship.paperAId] || 0) + 1;
    counts[relationship.paperBId] = (counts[relationship.paperBId] || 0) + 1;
  });
  
  return counts;
}

// Create complete graph data structure
export function createGraphData(papers: Paper[], relationships: PaperRelationship[]) {
  const relationshipCounts = calculateRelationshipCounts(papers, relationships);
  
  const nodes = transformPapersToNodes(papers).map(node => ({
    ...node,
    data: {
      ...node.data,
      relationshipCount: relationshipCounts[node.id] || 0,
    },
  }));
  
  const edges = transformRelationshipsToEdges(relationships);
  
  return { nodes, edges };
}

// Layout algorithms
export function applyForceLayout(nodes: Node[], edges: Edge[]): Node[] {
  // Simple force-directed layout simulation
  const iterations = 100;
  const k = Math.sqrt((800 * 600) / nodes.length); // Optimal distance
  const c = 0.01; // Cooling factor
  
  let temperature = 10;
  
  for (let iter = 0; iter < iterations; iter++) {
    // Calculate repulsive forces between all nodes
    nodes.forEach((nodeA, i) => {
      let fx = 0, fy = 0;
      
      nodes.forEach((nodeB, j) => {
        if (i !== j) {
          const dx = nodeA.position.x - nodeB.position.x;
          const dy = nodeA.position.y - nodeB.position.y;
          const distance = Math.sqrt(dx * dx + dy * dy) || 1;
          
          // Repulsive force
          const force = (k * k) / distance;
          fx += (dx / distance) * force;
          fy += (dy / distance) * force;
        }
      });
      
      // Calculate attractive forces from connected edges
      edges.forEach(edge => {
        if (edge.source === nodeA.id) {
          const targetNode = nodes.find(n => n.id === edge.target);
          if (targetNode) {
            const dx = targetNode.position.x - nodeA.position.x;
            const dy = targetNode.position.y - nodeA.position.y;
            const distance = Math.sqrt(dx * dx + dy * dy) || 1;
            
            // Attractive force
            const force = (distance * distance) / k;
            fx += (dx / distance) * force * 0.1;
            fy += (dy / distance) * force * 0.1;
          }
        }
      });
      
      // Apply forces with temperature cooling
      const displacement = Math.sqrt(fx * fx + fy * fy) || 1;
      nodeA.position.x += (fx / displacement) * Math.min(displacement, temperature);
      nodeA.position.y += (fy / displacement) * Math.min(displacement, temperature);
      
      // Keep nodes within bounds
      nodeA.position.x = Math.max(50, Math.min(750, nodeA.position.x));
      nodeA.position.y = Math.max(50, Math.min(550, nodeA.position.y));
    });
    
    temperature *= (1 - c);
  }
  
  return nodes;
}

// Circular layout for better initial positioning
export function applyCircularLayout(nodes: Node[]): Node[] {
  const centerX = 400;
  const centerY = 300;
  const radius = Math.min(centerX, centerY) * 0.8;
  
  return nodes.map((node, index) => {
    const angle = (2 * Math.PI * index) / nodes.length;
    return {
      ...node,
      position: {
        x: centerX + radius * Math.cos(angle),
        y: centerY + radius * Math.sin(angle),
      },
    };
  });
}

// Hierarchical layout based on relationship types
export function applyHierarchicalLayout(nodes: Node[], edges: Edge[]): Node[] {
  // Group nodes by their relationship patterns
  const levels: Record<number, Node[]> = {};
  const visited = new Set<string>();
  
  // Find root nodes (nodes with no incoming edges or most outgoing edges)
  const incomingCount: Record<string, number> = {};
  const outgoingCount: Record<string, number> = {};
  
  nodes.forEach(node => {
    incomingCount[node.id] = 0;
    outgoingCount[node.id] = 0;
  });
  
  edges.forEach(edge => {
    incomingCount[edge.target as string] = (incomingCount[edge.target as string] || 0) + 1;
    outgoingCount[edge.source as string] = (outgoingCount[edge.source as string] || 0) + 1;
  });
  
  // Start with nodes that have more outgoing than incoming connections
  const rootNodes = nodes.filter(node => 
    outgoingCount[node.id] >= incomingCount[node.id]
  ).sort((a, b) => 
    (outgoingCount[b.id] - incomingCount[b.id]) - (outgoingCount[a.id] - incomingCount[a.id])
  );
  
  // Assign levels using BFS
  let currentLevel = 0;
  let queue = rootNodes.slice(0, Math.max(1, Math.ceil(rootNodes.length / 3)));
  
  while (queue.length > 0) {
    if (!levels[currentLevel]) levels[currentLevel] = [];
    
    const nextQueue: Node[] = [];
    
    queue.forEach(node => {
      if (!visited.has(node.id)) {
        levels[currentLevel].push(node);
        visited.add(node.id);
        
        // Add connected nodes to next level
        edges.forEach(edge => {
          if (edge.source === node.id) {
            const targetNode = nodes.find(n => n.id === edge.target);
            if (targetNode && !visited.has(targetNode.id)) {
              nextQueue.push(targetNode);
            }
          }
        });
      }
    });
    
    queue = nextQueue;
    currentLevel++;
  }
  
  // Add any remaining unvisited nodes to the last level
  const unvisited = nodes.filter(node => !visited.has(node.id));
  if (unvisited.length > 0) {
    if (!levels[currentLevel]) levels[currentLevel] = [];
    levels[currentLevel].push(...unvisited);
  }
  
  // Position nodes in their levels
  const levelHeight = 120;
  const nodeWidth = 200;
  
  Object.keys(levels).forEach(levelKey => {
    const level = parseInt(levelKey);
    const levelNodes = levels[level];
    const startX = (800 - (levelNodes.length * nodeWidth)) / 2;
    
    levelNodes.forEach((node, index) => {
      node.position = {
        x: startX + (index * nodeWidth) + (nodeWidth / 2),
        y: 100 + (level * levelHeight),
      };
    });
  });
  
  return nodes;
}
