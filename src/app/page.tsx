"use client";

import { useEffect } from "react";
import { useProjectStore } from "@/store/projectStore";
import { CreateProjectForm } from "@/components/create-project-form";
import { ProjectList } from "@/components/project-list";
import { Layout, Spin, Alert, Space } from "antd";

const { Content } = Layout;

export default function Home() {
  const { projects, selectedProjectId, fetchProjects, isLoading, error } = useProjectStore();

  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  return (
    <Layout style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column' }}>
      <Content style={{ padding: '50px', flex: 1 }}>
        <div style={{ maxWidth: '900px', margin: '0 auto', background: '#fff', padding: '24px', borderRadius: '8px', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)' }}>
          <h1 style={{ textAlign: 'center', marginBottom: '24px', fontSize: '2em', fontWeight: 'bold' }}>Project Selection</h1>
          <div style={{ textAlign: 'center', marginBottom: '24px' }}>
            <CreateProjectForm />
          </div>
          {isLoading && (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Spin size="large">
                <p>Loading projects...</p>
              </Spin>
            </div>
          )}
          {error && (
            <Alert
              message="Error"
              description={error}
              type="error"
              showIcon
              style={{ marginBottom: '20px' }}
            />
          )}
          <ProjectList />
        </div>
      </Content>
    </Layout>
  );
}