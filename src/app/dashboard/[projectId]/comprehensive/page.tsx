"use client";

import { use } from "react";
import { MainDashboardLayout } from "@/components/main-dashboard-layout";
import { ComprehensiveDashboard } from "@/components/comprehensive-dashboard";
import { Typography } from "antd";

const { Title } = Typography;

interface ComprehensiveDashboardPageProps {
  params: Promise<{
    projectId: string;
  }>;
}

export default function ComprehensiveDashboardPage(props: ComprehensiveDashboardPageProps) {
  const params = use(props.params);
  const { projectId } = params;

  return (
    <MainDashboardLayout projectId={projectId}>
      <div className="h-[calc(100vh-120px)] w-full">
        <div className="px-4 sm:px-6 md:px-8 py-4 border-b border-gray-200">
          <Title level={2} className="!m-0 text-lg sm:text-xl md:text-2xl">
            Literature Management Dashboard
          </Title>
          <p className="mt-2 text-sm sm:text-base text-gray-600">
            Comprehensive research management with visual relationship mapping
          </p>
        </div>
        <div className="h-[calc(100%-80px)] w-full">
          <ComprehensiveDashboard projectId={projectId} />
        </div>
      </div>
    </MainDashboardLayout>
  );
}
