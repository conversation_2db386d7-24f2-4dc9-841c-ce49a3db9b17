"use client";

import { useEffect, use } from "react";
import { MainDashboardLayout } from "@/components/main-dashboard-layout";
import { useRelationshipStore } from "@/store/relationshipStore";
import { AddRelationshipForm } from "@/components/add-relationship-form";
import { RelationshipList } from "@/components/relationship-list";
import { Typography, Spin, Alert } from "antd";

const { Title } = Typography;

interface RelationshipsPageProps {
  params: Promise<{
    projectId: string;
  }>;
}

export default function RelationshipsPage(props: RelationshipsPageProps) {
  const params = use(props.params);
  const { projectId } = params;
  const { fetchRelationships, isLoading, error } = useRelationshipStore();

  useEffect(() => {
    if (projectId) {
      fetchRelationships(projectId);
    }
  }, [projectId, fetchRelationships]);

  return (
    <MainDashboardLayout projectId={projectId}>
      <div style={{ padding: '24px' }}>
        <Title level={2} style={{ marginBottom: '24px' }}>Paper Relationships</Title>
        <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '16px' }}>
          <AddRelationshipForm projectId={projectId} />
        </div>
        {isLoading && (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin size="large" tip="Loading relationships..." />
          </div>
        )}
        {error && (
          <Alert
            message="Error"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: '20px' }}
          />
        )}
        <RelationshipList projectId={projectId} />
      </div>
    </MainDashboardLayout>
  );
}