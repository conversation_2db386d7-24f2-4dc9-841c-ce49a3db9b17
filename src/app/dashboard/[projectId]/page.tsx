"use client";
import { use } from "react";

import { MainDashboardLayout } from "@/components/main-dashboard-layout";
import { Typography } from "antd";

const { Title, Text } = Typography;

interface DashboardPageProps {
  params: Promise<{
    projectId: string;
  }>;
}

export default function DashboardPage(props: DashboardPageProps) {
  const params = use(props.params);
  const { projectId } = params;

  return (
    <MainDashboardLayout projectId={projectId}>
      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
        <Title level={2}>Welcome to your Dashboard!</Title>
        <Text>Select a section from the sidebar to get started.</Text>
      </div>
    </MainDashboardLayout>
  );
}