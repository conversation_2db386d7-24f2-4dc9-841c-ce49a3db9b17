"use client";

import { useEffect, use } from "react";
import { MainDashboardLayout } from "@/components/main-dashboard-layout";
import { useQuestionStore } from "@/store/questionStore";
import { AddBigQuestionForm } from "@/components/add-big-question-form";
import { QuestionTreeView } from "@/components/question-tree-view";
import { Typography, Spin, Alert } from "antd";

const { Title } = Typography;

interface QuestionsPageProps {
  params: Promise<{
    projectId: string;
  }>;
}

export default function QuestionsPage(props: QuestionsPageProps) {
  const params = use(props.params);
  const { projectId } = params;
  const { fetchBigQuestions, fetchResearchQuestions, isLoading, error } = useQuestionStore();

  useEffect(() => {
    if (projectId) {
      fetchBigQuestions(projectId);
      fetchResearchQuestions(projectId);
    }
  }, [projectId, fetchBigQuestions, fetchResearchQuestions]);

  return (
    <MainDashboardLayout projectId={projectId}>
      <div style={{ padding: '24px' }}>
        <Title level={2} style={{ marginBottom: '24px' }}>Research Questions</Title>
        <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '16px' }}>
          <AddBigQuestionForm projectId={projectId} />
        </div>
        {isLoading && (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin size="large" tip="Loading questions..." />
          </div>
        )}
        {error && (
          <Alert
            message="Error"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: '20px' }}
          />
        )}
        <QuestionTreeView projectId={projectId} />
      </div>
    </MainDashboardLayout>
  );
}