"use client";;
import { use } from "react";

import { useRouter } from "next/navigation";
import { EditPaperForm } from "@/components/edit-paper-form";
import { MainDashboardLayout } from "@/components/main-dashboard-layout";
import { Typography } from "antd";

const { Title } = Typography;

interface EditPaperPageProps {
  params: Promise<{
    projectId: string;
    paperId: string;
  }>;
}

export default function EditPaperPage(props: EditPaperPageProps) {
  const params = use(props.params);
  const { projectId, paperId } = params;
  const router = useRouter();

  const handleSuccess = () => {
    router.push(`/dashboard/${projectId}/papers/${paperId}`);
  };

  return (
    <MainDashboardLayout projectId={projectId}>
      <div style={{ padding: '24px' }}>
        <Title level={2} style={{ marginBottom: '24px' }}>Edit Paper</Title>
        <EditPaperForm projectId={projectId} paperId={paperId} onSuccess={handleSuccess} />
      </div>
    </MainDashboardLayout>
  );
}