"use client";;
import { use } from "react";

import { PaperDetailView } from "@/components/paper-detail-view";
import { MainDashboardLayout } from "@/components/main-dashboard-layout";
import { Typography } from "antd";

const { Title } = Typography;

interface SinglePaperPageProps {
  params: Promise<{
    projectId: string;
    paperId: string;
  }>;
}

export default function SinglePaperPage(props: SinglePaperPageProps) {
  const params = use(props.params);
  const { projectId, paperId } = params;

  return (
    <MainDashboardLayout projectId={projectId}>
      <div style={{ padding: '24px' }}>
        <Title level={2} style={{ marginBottom: '24px' }}>Paper Details</Title>
        <PaperDetailView projectId={projectId} paperId={paperId} />
      </div>
    </MainDashboardLayout>
  );
}