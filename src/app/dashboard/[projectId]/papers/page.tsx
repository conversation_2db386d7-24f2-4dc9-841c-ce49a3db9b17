"use client";

import { useEffect, use } from "react";
import { AddPaperForm } from "@/components/add-paper-form";
import { PaperList } from "@/components/paper-list";
import { usePaperStore } from "@/store/paperStore";
import { MainDashboardLayout } from "@/components/main-dashboard-layout";
import { Typography, Space, Spin, Alert } from "antd";

const { Title } = Typography;

interface PapersPageProps {
  params: Promise<{
    projectId: string;
  }>;
}

export default function PapersPage(props: PapersPageProps) {
  const params = use(props.params);
  const { projectId } = params;
  const { fetchPapers, isLoading, error } = usePaperStore();

  useEffect(() => {
    if (projectId) {
      fetchPapers(projectId);
    }
  }, [projectId, fetchPapers]);

  return (
    <MainDashboardLayout projectId={projectId}>
      <div style={{ padding: '24px' }}>
        <Title level={2} style={{ marginBottom: '24px' }}>Paper Management</Title>
        <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '16px' }}>
          <AddPaperForm projectId={projectId} />
        </div>
        {isLoading && (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin size="large" tip="Loading papers..." />
          </div>
        )}
        {error && (
          <Alert
            message="Error"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: '20px' }}
          />
        )}
        <PaperList projectId={projectId} />
      </div>
    </MainDashboardLayout>
  );
}