"use client";

import { use } from "react";
import { MainDashboardLayout } from "@/components/main-dashboard-layout";
import { VisualMapping } from "@/components/visual-mapping";
import { Typography } from "antd";

const { Title } = Typography;

interface VisualMapPageProps {
  params: Promise<{
    projectId: string;
  }>;
}

export default function VisualMapPage(props: VisualMapPageProps) {
  const params = use(props.params);
  const { projectId } = params;

  return (
    <MainDashboardLayout projectId={projectId}>
      <div className="h-[calc(100vh-120px)] w-full">
        <div className="px-4 sm:px-6 md:px-8 py-4 border-b border-gray-200">
          <Title level={2} className="!m-0 text-lg sm:text-xl md:text-2xl">
            Visual Relationship Map
          </Title>
          <p className="mt-2 text-sm sm:text-base text-gray-600">
            Interactive visualization of papers and their relationships
          </p>
        </div>
        <div className="h-[calc(100%-80px)] w-full">
          <VisualMapping projectId={projectId} />
        </div>
      </div>
    </MainDashboardLayout>
  );
}
