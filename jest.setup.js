import React from 'react';
import '@testing-library/jest-dom'

// Mock React Flow
jest.mock('reactflow', () => ({
  ReactFlow: ({ children, ...props }) => <div data-testid="react-flow" {...props}>{children}</div>,
  Background: () => <div data-testid="react-flow-background" />,
  Controls: () => <div data-testid="react-flow-controls" />,
  MiniMap: () => <div data-testid="react-flow-minimap" />,
  Panel: ({ children, ...props }) => <div data-testid="react-flow-panel" {...props}>{children}</div>,
  Handle: (props) => <div data-testid="react-flow-handle" {...props} />,
  Position: {
    Top: 'top',
    Bottom: 'bottom',
    Left: 'left',
    Right: 'right',
  },
  useReactFlow: () => ({
    fitView: jest.fn(),
    zoomIn: jest.fn(),
    zoomOut: jest.fn(),
  }),
  ReactFlowProvider: ({ children }) => <div data-testid="react-flow-provider">{children}</div>,
  addEdge: jest.fn(),
  applyNodeChanges: jest.fn(),
  applyEdgeChanges: jest.fn(),
  getBezierPath: jest.fn(() => ['M0,0 L100,100', 50, 50]),
  EdgeLabelRenderer: ({ children }) => <div data-testid="edge-label-renderer">{children}</div>,
}))

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: React.forwardRef(({ children, ...props }, ref) => {
      const { whileHover, animate, initial, transition, ...rest } = props;
      return <div ref={ref} {...rest}>{children}</div>;
    }),
    path: React.forwardRef(({ children, ...props }, ref) => {
      const { whileHover, animate, initial, transition, ...rest } = props;
      return <path ref={ref} {...rest}>{children}</path>;
    }),
  },
  AnimatePresence: ({ children }) => <div>{children}</div>,
}))

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  usePathname: () => '/test-path',
  useSearchParams: () => new URLSearchParams(),
}))

// Mock Ant Design components that might cause issues
jest.mock('antd', () => ({
  Layout: {
    Header: ({ children, ...props }) => <header {...props}>{children}</header>,
    Sider: ({ children, ...props }) => <aside {...props}>{children}</aside>,
    Content: ({ children, ...props }) => <main {...props}>{children}</main>,
  },
  Menu: ({ children, ...props }) => <nav {...props}>{children}</nav>,
  'Menu.Item': ({ children, ...props }) => <div {...props}>{children}</div>,
  Typography: {
    Title: ({ children, ...props }) => <h1 {...props}>{children}</h1>,
    Text: ({ children, ...props }) => <span {...props}>{children}</span>,
  },
  Button: ({ children, ...props }) => <button {...props}>{children}</button>,
  Spin: ({ children, ...props }) => <div {...props}>{children}</div>,
  Alert: ({ children, ...props }) => <div {...props}>{children}</div>,
  Card: ({ children, ...props }) => <div {...props}>{children}</div>,
  Table: ({ children, ...props }) => <table {...props}>{children}</table>,
  Modal: {
    confirm: jest.fn(),
  },
  Space: ({ children, ...props }) => <div {...props}>{children}</div>,
  ConfigProvider: ({ children }) => <div>{children}</div>,
  Tag: ({ children, ...props }) => <span {...props}>{children}</span>,
}))

// Global test utilities
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}))

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})
