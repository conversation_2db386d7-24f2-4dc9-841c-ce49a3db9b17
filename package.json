{"name": "research-manager", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@ant-design/v5-patch-for-react-19": "^1.0.3", "@prisma/client": "^6.9.0", "@types/d3": "^7.4.3", "@types/node-fetch": "^2.6.12", "antd": "^5.26.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3-force": "^3.0.0", "d3-zoom": "^3.0.0", "framer-motion": "^12.19.1", "lucide-react": "^0.515.0", "next": "15.3.4", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "prisma": "^6.9.0", "react": "19.1.0", "react-dom": "19.1.0", "react-spring": "^10.0.1", "reactflow": "^11.11.4", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.5"}, "devDependencies": {"@types/node": "^20", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "eslint": "^8", "eslint-config-next": "15.3.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}