# Testing Strategy for Visual Relationship Mapping

## Overview

This document outlines the comprehensive testing strategy for the visual relationship mapping features added to the research manager application.

## Testing Framework

- **Jest**: Primary testing framework
- **React Testing Library**: For component testing
- **@testing-library/jest-dom**: For enhanced DOM assertions
- **@testing-library/user-event**: For user interaction testing

## Test Categories

### 1. Unit Tests

#### Graph Utilities (`src/lib/graph-utils.ts`)
- ✅ Data transformation functions
- ✅ Layout algorithms
- ✅ Relationship calculations
- ✅ Constants and configurations

**Coverage Areas:**
- `transformPapersToNodes()` - Converts paper data to graph nodes
- `transformRelationshipsToEdges()` - Converts relationships to graph edges
- `calculateRelationshipCounts()` - Counts connections per paper
- `createGraphData()` - Combines papers and relationships into graph structure
- Layout algorithms: `applyCircularLayout()`, `applyForceLayout()`, `applyHierarchicalLayout()`

#### State Management (`src/store/graphStore.ts`)
- ✅ Store initialization
- ✅ Node and edge selection
- ✅ Layout management
- ✅ View controls (minimap, controls, background)
- ✅ Filtering functionality
- ✅ Data loading

**Coverage Areas:**
- Selection state management (single and multi-select)
- Layout switching and application
- Filter state (search, relationship types, year range)
- View preferences persistence

### 2. Component Tests

#### Graph Components
- ✅ `PaperNode` - Paper node rendering and states
- `RelationshipEdge` - Edge rendering and styling
- `GraphControls` - Layout controls and interactions
- `GraphStats` - Statistics display and updates
- `VisualMapping` - Main graph container

**Coverage Areas:**
- Rendering with different props
- Selected/hovered states
- Responsive behavior
- Event handling
- Accessibility features

#### Navigation Components
- `MobileNavigation` - Mobile menu functionality
- `DashboardSidebar` - Updated navigation with visual map link

### 3. Integration Tests

#### Graph Interaction Flow
- Node selection and deselection
- Layout switching
- Filter application
- Data loading and transformation
- Responsive layout changes

#### Navigation Flow
- Route transitions to visual map
- Mobile navigation menu
- Sidebar navigation updates

### 4. Visual Regression Tests (Future)

#### Graph Layouts
- Force-directed layout appearance
- Circular layout positioning
- Hierarchical layout structure
- Node and edge styling consistency

#### Responsive Design
- Mobile layout adaptations
- Tablet view optimizations
- Desktop full-feature display

## Test Data

### Mock Data Structure
```typescript
// Papers
const mockPapers = [
  {
    id: '1',
    title: 'Sample Research Paper',
    authors: 'John Doe, Jane Smith',
    publicationYear: 2023,
    link: 'https://example.com/paper1',
    notes: 'Important findings on topic X',
    projectId: 'project-1',
    createdAt: '2023-01-01',
    updatedAt: '2023-01-01',
  }
]

// Relationships
const mockRelationships = [
  {
    id: 'rel-1',
    paperAId: '1',
    paperBId: '2',
    type: 'SUPPORTS',
    notes: 'Paper A supports the findings in Paper B',
    projectId: 'project-1',
  }
]
```

## Mocking Strategy

### External Dependencies
- **React Flow**: Mocked to prevent canvas rendering issues in tests
- **Framer Motion**: Simplified motion components for testing
- **Next.js Router**: Mocked navigation functions
- **Ant Design**: Simplified component mocks

### Browser APIs
- `ResizeObserver`: Mocked for responsive components
- `window.matchMedia`: Mocked for media queries

## Test Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- graph-utils.test.ts

# Run tests matching pattern
npm test -- --testNamePattern="layout"
```

## Coverage Goals

- **Overall Coverage**: 80%+
- **Critical Functions**: 95%+
- **UI Components**: 75%+
- **State Management**: 90%+

### Critical Areas Requiring High Coverage
1. Graph data transformation logic
2. Layout algorithms
3. Selection and interaction handling
4. State management stores
5. Navigation and routing

## Testing Best Practices

### 1. Test Structure
- Use descriptive test names
- Group related tests with `describe` blocks
- Follow AAA pattern (Arrange, Act, Assert)

### 2. Component Testing
- Test behavior, not implementation
- Use semantic queries (getByRole, getByLabelText)
- Test user interactions with user-event
- Verify accessibility attributes

### 3. State Testing
- Test state transitions
- Verify side effects
- Use renderHook for custom hooks
- Test error conditions

### 4. Mock Management
- Keep mocks simple and focused
- Reset mocks between tests
- Mock at the appropriate level

## Continuous Integration

### Pre-commit Hooks
- Run linting
- Run type checking
- Run unit tests
- Check test coverage

### CI Pipeline
- Run full test suite
- Generate coverage reports
- Fail on coverage below threshold
- Run tests on multiple Node.js versions

## Performance Testing

### Graph Performance
- Test with large datasets (100+ nodes, 200+ edges)
- Measure layout calculation times
- Monitor memory usage during interactions
- Test scroll and zoom performance

### Component Performance
- Test re-render frequency
- Measure component mount times
- Profile state update performance

## Accessibility Testing

### Graph Accessibility
- Keyboard navigation support
- Screen reader compatibility
- Color contrast compliance
- Focus management

### Component Accessibility
- ARIA labels and roles
- Semantic HTML structure
- Keyboard interaction support

## Future Enhancements

### Visual Testing
- Implement visual regression testing with tools like Percy or Chromatic
- Test graph layout consistency across browsers
- Verify responsive design breakpoints

### E2E Testing
- Add Playwright or Cypress for end-to-end testing
- Test complete user workflows
- Cross-browser compatibility testing

### Performance Monitoring
- Add performance benchmarks
- Monitor bundle size impact
- Track runtime performance metrics

## Maintenance

### Regular Tasks
- Update test data to match schema changes
- Review and update mocks for dependency updates
- Maintain test coverage as features are added
- Refactor tests to improve maintainability

### Documentation
- Keep testing strategy updated
- Document new testing patterns
- Maintain mock documentation
- Update coverage goals as needed
